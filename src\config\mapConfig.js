/**
 * 地图配置模块
 * 包含地图数据和相关配置
 */

/**
 * 地图配置类
 */
export class MapConfig {

  /**
   * 将位置点数据转换为3D场景对象格式
   */
  static convertPositionDataToSceneObjects(positionData) {
    return positionData.map(pos => ({
      name: pos.name,
      type: 'person', // 默认类型为person
      position: {
        x: parseFloat(pos.xcoordinate) || 0,
        y: parseFloat(pos.ycoordinate) || 0,
        z: parseFloat(pos.zcoordinate) || 0
      },
      rotation: (parseFloat(pos.yaw) || 0) * Math.PI / 180, // 将角度转换为弧度
      actionNo: pos.actionNo, // 保留动作编号
      contentDetail: pos.contentDetail, // 保留内容详情
      uuid: pos.uuid, // 保存UUID
      isSaved: true // 从服务器加载的数据标记为已保存
    }))
  }

  /**
   * 收集当前地图的所有位置点信息
   */
  static collectMapPositions(treeData, currentMapId, sceneObjects) {
    const positions = []

    // 从树形数据中获取位置点信息，确保类型匹配
    const mapIdStr = currentMapId.toString()
    const mapNode = treeData.find(node => node.key === mapIdStr || node.key === currentMapId)
    if (mapNode && mapNode.children) {
      mapNode.children.forEach(child => {
        if (child.isLeaf && child.objectData) {
          const posData = child.objectData

          // 查找对应的3D对象以获取最新的坐标和旋转信息
          const sceneObject = sceneObjects.find(obj => obj.uuid === posData.uuid)

          const position = {
            uuid: posData.uuid || null,
            name: posData.name || '',
            xcoordinate: sceneObject ? sceneObject.position.x.toString() : (posData.xcoordinate || '0'),
            ycoordinate: sceneObject ? sceneObject.position.y.toString() : (posData.ycoordinate || '0'),
            zcoordinate: sceneObject ? sceneObject.position.z.toString() : (posData.zcoordinate || '0'),
            yaw: sceneObject && sceneObject.getRotationDegrees ?
                 sceneObject.getRotationDegrees().toString() : (posData.yaw || '0'),
            actionNo: posData.actionNo || '',
            contentDetail: posData.contentDetail || '',
            mapId: currentMapId
          }

          positions.push(position)
        }
      })
    }

    return positions
  }
}
