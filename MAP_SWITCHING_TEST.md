# 地图切换清理功能测试指南

## 问题描述
之前存在的问题：当用户打开一个地图并渲染PGM和PCD后，切换到另一个地图时，之前地图的PCD渲染没有被清除，导致多个地图的点云叠加显示。

## 修复内容

### 1. 增强的清理机制
- **强制清理方法**：为PGM和PCD渲染器添加了`forceCleanup()`方法
- **场景对象清理**：自动识别并移除所有PGM和PCD相关的3D对象
- **资源释放**：正确释放几何体、材质和纹理资源
- **状态重置**：重置所有相关的内部状态变量

### 2. 优化的地图切换流程
```javascript
// 地图切换时的清理顺序：
1. 清理场景对象 (SceneSetup.clearScene)
2. 清理PCD渲染 (clearPCDRendering)
3. 清理PGM渲染 (clearPGMRendering)
4. 重置选中状态
5. 重置拖拽管理器边界
6. 恢复默认地板和网格
```

### 3. 详细的调试日志
添加了详细的控制台日志，帮助跟踪清理过程：
- 地图切换开始
- PCD清理状态
- PGM清理状态
- 资源释放确认
- 清理完成确认

## 测试步骤

### 测试场景1：基本地图切换
1. **打开第一个地图**
   - 选择一个地图（如map1）
   - 点击"渲染PGM"
   - 点击"渲染PCD"
   - 确认地图和点云正常显示

2. **切换到第二个地图**
   - 选择另一个地图（如map2）
   - 观察控制台日志，应该看到：
     ```
     开始处理地图信息: map2
     开始清理场景...
     开始清理所有地图渲染...
     清理PCD渲染...
     PCD渲染状态: true
     PCD网格存在: true
     强制清理PCD资源...
     已移除PCD对象: pcd-point-cloud
     PCD资源强制清理完成
     PCD渲染已清理
     清理PGM渲染...
     强制清理PGM资源...
     已移除PGM对象: pgm-map
     PGM资源强制清理完成
     PGM渲染已清理
     所有地图渲染清理完成
     场景清理完成
     ```

3. **验证清理效果**
   - 确认第一个地图的PCD点云完全消失
   - 确认第一个地图的PGM地图完全消失
   - 确认只显示默认的地板网格
   - 确认性能监控面板显示点数为0

### 测试场景2：多次切换
1. **连续切换多个地图**
   - 依次切换map1 → map2 → map3 → map1
   - 每次切换都渲染PGM和PCD
   - 确认每次切换都正确清理了之前的渲染

2. **验证内存使用**
   - 打开性能监控面板
   - 观察内存使用情况
   - 确认内存没有持续增长（无内存泄漏）

### 测试场景3：部分渲染切换
1. **只渲染PGM后切换**
   - 打开map1，只渲染PGM
   - 切换到map2
   - 确认PGM被正确清理

2. **只渲染PCD后切换**
   - 打开map1，只渲染PCD
   - 切换到map2
   - 确认PCD被正确清理

### 测试场景4：性能监控验证
1. **监控渲染状态**
   - 打开性能监控面板
   - 切换地图前后对比点数变化
   - 确认点数正确重置为0

2. **监控内存使用**
   - 观察内存使用变化
   - 确认切换后内存得到释放

## 预期结果

### ✅ 正确的行为
- **完全清理**：切换地图时，之前地图的所有渲染内容完全消失
- **无叠加**：新地图的渲染不会与旧地图叠加
- **状态重置**：所有相关状态（isPCDRendered等）正确重置
- **内存释放**：WebGL资源得到正确释放，无内存泄漏
- **性能稳定**：多次切换后性能保持稳定

### ❌ 需要注意的问题
- **渲染残留**：如果仍有旧地图的渲染内容残留
- **状态错误**：如果按钮状态与实际渲染状态不符
- **内存泄漏**：如果内存使用持续增长
- **性能下降**：如果多次切换后FPS明显下降

## 调试信息

### 控制台日志关键词
- `开始处理地图信息` - 地图切换开始
- `强制清理PCD资源` - PCD清理过程
- `强制清理PGM资源` - PGM清理过程
- `已移除PCD对象` - PCD对象移除确认
- `已移除PGM对象` - PGM对象移除确认
- `场景清理完成` - 整体清理完成

### 性能监控指标
- **点数**：切换后应该重置为0
- **内存**：应该有明显下降
- **FPS**：应该保持稳定或提升

## 技术实现细节

### PCD强制清理
```javascript
forceCleanup() {
  // 查找所有PCD相关对象
  const pcdObjects = this.scene.children.filter(child => 
    child.name && (child.name.includes('pcd') || child.userData.type === 'pcd')
  )
  
  // 逐个移除并释放资源
  pcdObjects.forEach(obj => {
    this.scene.remove(obj)
    if (obj.geometry) obj.geometry.dispose()
    if (obj.material) obj.material.dispose()
  })
}
```

### PGM强制清理
```javascript
forceCleanup() {
  // 查找所有PGM相关对象
  const pgmObjects = this.scene.children.filter(child => 
    child.name && (child.name.includes('pgm') || child.userData.type === 'pgm')
  )
  
  // 逐个移除并释放资源
  pgmObjects.forEach(obj => {
    this.scene.remove(obj)
    if (obj.geometry) obj.geometry.dispose()
    if (obj.material) {
      if (obj.material.map) obj.material.map.dispose()
      obj.material.dispose()
    }
  })
}
```

## 总结
通过这些修复，地图切换功能现在能够：
1. **完全清理**之前地图的所有渲染内容
2. **正确释放**WebGL资源，避免内存泄漏
3. **重置状态**，确保UI与实际状态一致
4. **提供详细日志**，便于调试和验证

这确保了用户在切换地图时获得干净、一致的体验，同时保持良好的性能表现。
