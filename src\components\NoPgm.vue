<template>
  <div class="map3d-container">
    <!-- 第一列：位置列表 -->
    <div class="map-list-panel">
      <h3>位置列表</h3>

      <!-- 如果没有选择地图，显示空状态 -->
      <a-empty v-if="!currentMapId" description="请打开地图以展示位置" :image="false">
        <template #description>
          <span style="color: #999;">请打开地图以展示位置</span>
        </template>
      </a-empty>

      <!-- 如果已选择地图，显示树形结构 -->
      <a-tree v-else v-model:expandedKeys="expandedKeys" v-model:selectedKeys="selectedKeys" :load-data="onLoadData"
        :tree-data="treeData" @select="onTreeSelect" @expand="onTreeExpand">
        <template #title="{ title }">
          <span>{{ title }}</span>
        </template>
      </a-tree>
    </div>

    <!-- 第二列：3D容器 -->
    <div class="three-container-wrapper">
      <!-- 操作区 -->
      <div class="map-operations">
        <div class="map-buttons">
          <a-button type="primary"  @click="showMapSelectModal">
            打开地图
          </a-button>
          <a-button type="default" @click="saveMap" :disabled="!currentMapId">
            保存
          </a-button>
        </div>

        <!-- 拖动位置元素 -->
        <div class="drag-position-item" draggable="true" @dragstart="startDrag('person', $event)">
          <span>👤 位置</span>
        </div>
      </div>

      <!-- 3D容器 -->
      <div ref="threeContainer" class="three-container"></div>
    </div>

    <!-- 地图选择模态框 -->
    <a-modal v-model:visible="mapSelectModalVisible" title="选择地图" @ok="handleMapSelect" @cancel="handleMapSelectCancel"
      okText="打开" cancelText="取消">
      <a-select v-model:value="selectedMapForModal" placeholder="请选择地图" style="width: 100%;" size="small">
        <a-select-option v-for="map in mapList" :key="map.mapId" :value="map.mapId">
          {{ map.mapName }}
        </a-select-option>
      </a-select>
    </a-modal>

    <!-- 第三列：属性编辑 -->
    <div class="control-panel">
      <!-- 未选中时的空状态 -->
      <a-empty v-if="!selectedPositionData" class="empty-state" description="请选择位置元素以编辑属性" :image="false">
        <template #description>
          <span style="color: #999;">请选择位置元素以编辑属性</span>
        </template>
      </a-empty>

      <!-- 选中时的属性编辑 -->
      <div v-else class="object-properties">

        <h3>属性编辑</h3>

        <div class="property-group inline">
          <label>位置ID:</label>
          <a-input v-model:value="selectedPositionData.id" disabled size="small" />
        </div>
        <div class="property-group inline">
          <label>位置名称:</label>
          <a-input v-model:value="selectedPositionData.name"
            @change="e => updatePositionProperty('name', e.target.value)" size="small" />
        </div>
        <div class="property-group inline">
          <label>X坐标:</label>
          <a-input-number v-model:value="selectedPositionData.xcoordinate"
            @change="val => updatePositionProperty('xcoordinate', val)" size="small" />
        </div>
        <div class="property-group inline">
          <label>Y坐标:</label>
          <a-input-number v-model:value="selectedPositionData.ycoordinate"
            @change="val => updatePositionProperty('ycoordinate', val)" size="small" />
        </div>
        <div class="property-group inline">
          <label>Z坐标:</label>
          <a-input-number v-model:value="selectedPositionData.zcoordinate"
            @change="val => updatePositionProperty('zcoordinate', val)" size="small" />
        </div>
        <div class="property-group inline">
          <a-tooltip placement="top">
            <template #title>
              <div>
                0°=正面朝前, 90°=朝右, 180°=朝后, 270°=朝左<br>
                交互方式：Shift+滚轮 | 拖拽圆盘 | Q/E | Ctrl+左右箭头
              </div>
            </template>
            <label style="cursor: help;">Yaw(°):</label>
          </a-tooltip>
          <a-input-number :min="0" :max="360" :step="15" :value="Number(selectedPositionData.yaw || 0)"
            @change="val => updatePositionProperty('yaw', String(val))" size="small" />
        </div>
        <div class="property-group inline">
          <label>动作编号:</label>
          <a-input v-model:value="selectedPositionData.actionNo"
            @change="e => updatePositionProperty('actionNo', e.target.value)" size="small" />
        </div>
        <div class="property-group block">
          <label>内容详情:</label>
          <a-textarea v-model:value="selectedPositionData.contentDetail"
            @change="e => updatePositionProperty('contentDetail', e.target.value)" :rows="10" size="small" />
        </div>
        <!-- 操作按钮 -->
        <div class="position-actions">
          <a-button block danger @click="deleteSelectedPosition">
            删除位置
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { Person } from '../classes/Person.js'
import { DragManager } from '../managers/DragManager.js'
import { markRaw } from 'vue'
import { createNonReactiveThreeObject, makeThreeObjectNonReactive } from '../utils/threeUtils.js'
import { generateUUID } from '../utils/common.js'
import { message, Modal } from 'ant-design-vue'

export default {
  name: 'Map3D',
  data() {
    return {
      scene: null, // 场景
      camera: null, // 相机
      renderer: null, // 渲染器
      controls: null, // 控制器
      dragManager: null, // 拖拽管理器
      sceneObjects: [], // 场景对象
      selectedObject: null, // 选中的对象
      selectedPositionData: null, // 选中的位置点数据（统一属性面板）
      nextId: 1,
      // 当前选中的地图ID（用于拖拽时确定插入位置）
      currentSelectedMapId: null,
      // 旋转相关
      hintTimeout: null,
      // 键盘状态跟踪
      isShiftPressed: false,
      // 树形数据相关
      treeData: [],
      expandedKeys: [],
      selectedKeys: [],
      // 地图数据
      mapList: [
        {
          mapName: '办公区域',
          mapId: 'map001',
          mapJSON: 'xxxxxx'
        },
        {
          mapName: '操作区',
          mapId: 'map002',
          mapJSON: 'xxxxxx'
        },
      ],
      currentMapId: null,
      // 地图选择模态框相关
      mapSelectModalVisible: false,
      selectedMapForModal: null,
      // 位置计数器（每个地图独立计数）
      positionCounters: {}
    }
  },

  mounted() {
    this.initThreeJS()
    this.setupDragAndDrop()
    this.animate()

    // 添加键盘事件监听
    document.addEventListener('keydown', this.onKeyDown)
    document.addEventListener('keydown', this.onKeyStateChange)
    document.addEventListener('keyup', this.onKeyStateChange)
  },
  beforeUnmount() {
    if (this.renderer) {
      this.renderer.dispose()
    }
    // 清理事件监听
    document.removeEventListener('keydown', this.onKeyDown)
    document.removeEventListener('keydown', this.onKeyStateChange)
    document.removeEventListener('keyup', this.onKeyStateChange)
  },
  methods: {
    // 显示地图选择模态框
    showMapSelectModal() {
      this.mapSelectModalVisible = true
    },

    // 处理地图选择确认
    async handleMapSelect() {
      if (this.selectedMapForModal) {
        await this.loadMapWithPositions(this.selectedMapForModal)
        this.mapSelectModalVisible = false
      }
    },

    // 处理地图选择取消
    handleMapSelectCancel() {
      this.mapSelectModalVisible = false
      this.selectedMapForModal = null
    },

    // 保存地图
    saveMap() {
      if (!this.currentMapId) {
        message.warning('请先选择一个地图')
        return
      }

      // 收集当前地图的所有位置点信息
      const positionList = this.collectCurrentMapPositions()

      // 输出位置点信息（按照指定格式）
      console.log('保存地图:', this.currentMapId)
      console.log('位置点信息:', JSON.stringify(positionList))

      // 这里可以调用API保存到后端
      // await this.saveMapToServer(this.currentMapId, positionList)

     message.success(`地图保存成功，共保存 ${positionList.length} 个位置点`)
    },

    // 收集当前地图的所有位置点信息
    collectCurrentMapPositions() {
      const positions = []

      // 从树形数据中获取位置点信息
      const mapNode = this.treeData.find(node => node.key === this.currentMapId)
      if (mapNode && mapNode.children) {
        mapNode.children.forEach(child => {
          if (child.isLeaf && child.objectData) {
            const posData = child.objectData

            // 查找对应的3D对象以获取最新的坐标和旋转信息
            const sceneObject = this.sceneObjects.find(obj => obj.name === posData.name)

            const position = {
              id: posData.id || null, // 如果ID为空则设为null（新建元素）
              name: posData.name || '',
              xcoordinate: sceneObject ? sceneObject.position.x.toString() : (posData.xcoordinate || '0'),
              ycoordinate: sceneObject ? sceneObject.position.y.toString() : (posData.ycoordinate || '0'),
              zcoordinate: sceneObject ? sceneObject.position.z.toString() : (posData.zcoordinate || '0'),
              yaw: sceneObject && sceneObject.getRotationDegrees ?
                   sceneObject.getRotationDegrees().toString() : (posData.yaw || '0'),
              actionNo: posData.actionNo || '',
              contentDetail: posData.contentDetail || '',
              mapId: this.currentMapId
            }

            positions.push(position)
          }
        })
      }

      return positions
    },

    // 删除选中的位置
    deleteSelectedPosition() {
      if (!this.selectedPositionData) {
        message.warning('请先选择一个位置')
        return
      }

      Modal.confirm({
        title: '确认删除',
        content: `确定要删除位置"${this.selectedPositionData.name}"吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.performDeletePosition()
        }
      })
    },

    // 执行删除位置操作
    performDeletePosition() {
      if (!this.selectedPositionData) return

      // 从3D场景中删除对应对象
      const sceneObjectIndex = this.sceneObjects.findIndex(obj =>
        obj.name === this.selectedPositionData.name
      )

      if (sceneObjectIndex !== -1) {
        const object = this.sceneObjects[sceneObjectIndex]
        this.scene.remove(object.mesh)
        this.sceneObjects.splice(sceneObjectIndex, 1)
      }

      // 从树形数据中删除
      const mapNode = this.treeData.find(node => node.key === this.currentMapId)
      if (mapNode && mapNode.children) {
        const positionIndex = mapNode.children.findIndex(child =>
          child.objectData.name === this.selectedPositionData.name
        )
        if (positionIndex !== -1) {
          mapNode.children.splice(positionIndex, 1)
          this.treeData = [...this.treeData]
        }
      }

      // 清空选择
      this.selectedPositionData = null
      this.selectedObject = null
      this.selectedKeys = []

      message.success('位置删除成功')
    },

    // 获取当前地图的位置计数
    getPositionCountForMap(mapId) {
      if (!this.positionCounters[mapId]) {
        this.positionCounters[mapId] = 1
      }
      return this.positionCounters[mapId]++
    },

    // 初始化树形数据
    initTreeData() {
      this.treeData = this.mapList.map(map => ({
        title: map.mapName,
        key: map.mapId,
        isLeaf: false,
        children: []
      }))
    },

    // 异步加载树节点数据（现在只是占位，实际加载在点击时进行）
    async onLoadData(treeNode) {
      console.log('onLoadData', treeNode)
      // 如果已经有子节点，直接返回
      if (treeNode.dataRef.children) {
        return
      }

      // 初始化为空数组，等待用户点击时加载
      treeNode.dataRef.children = []
      this.treeData = [...this.treeData]
    },

    // 根据mapId查询位置点数据
    async fetchPositionsByMapId(mapId) {
      // 这里应该是真实的API调用，目前使用模拟数据
      return new Promise((resolve) => {
        setTimeout(() => {
          // 模拟不同地图的位置点数据，按照您的数据结构
          const mockData = {
            'map001': [
              {
                id: 'pos001',
                name: '沙盘',
                xcoordinate: '0',
                ycoordinate: '0',
                zcoordinate: '-6',
                yaw: '0',
                actionNo: 'ACT001',
                contentDetail: '欢迎来到沙盘'
              },
              {
                id: 'pos002',
                name: '数字化样板间',
                xcoordinate: '-3',
                ycoordinate: '0',
                zcoordinate: '0',
                yaw: '90',
                actionNo: 'ACT002',
                contentDetail: '欢迎来到数字化样板间'
              },
              {
                id: 'pos003',
                name: '产品墙',
                xcoordinate: '3',
                ycoordinate: '0',
                zcoordinate: '2',
                yaw: '180',
                actionNo: 'ACT003',
                contentDetail: '欢迎来到产品墙'
              }
            ],
            'map002': [
              {
                id: 'pos004',
                name: '历史墙',
                xcoordinate: '-5',
                ycoordinate: '0',
                zcoordinate: '5',
                yaw: '45',
                actionNo: 'ACT004',
                contentDetail: '欢迎来到历史墙'
              },
              {
                id: 'pos005',
                name: '前处理车间',
                xcoordinate: '5',
                ycoordinate: '0',
                zcoordinate: '5',
                yaw: '135',
                actionNo: 'ACT005',
                contentDetail: '欢迎来到前处理车间'
              },
              {
                id: 'pos006',
                name: '中央控制室',
                xcoordinate: '0',
                ycoordinate: '0',
                zcoordinate: '8',
                yaw: '180',
                actionNo: 'ACT006',
                contentDetail: '欢迎来到中央控制室'
              },
              {
                id: 'pos007',
                name: '酸奶发酵暂存缸',
                xcoordinate: '-2',
                ycoordinate: '0',
                zcoordinate: '-3',
                yaw: '270',
                actionNo: 'ACT007',
                contentDetail: '欢迎来到酸奶发酵暂存缸'
              }
            ],
          }

          resolve(mockData[mapId] || [])
        }, 500) // 模拟网络延迟
      })
    },

    // 将位置点数据转换为3D场景对象格式
    convertPositionDataToSceneObjects(positionData) {
      return positionData.map(pos => ({
        name: pos.name,
        type: 'person', // 默认类型为person
        position: {
          x: parseFloat(pos.xcoordinate) || 0,
          y: parseFloat(pos.ycoordinate) || 0,
          z: parseFloat(pos.zcoordinate) || 0
        },
        rotation: (parseFloat(pos.yaw) || 0) * Math.PI / 180, // 将角度转换为弧度
        actionNo: pos.actionNo, // 保留动作编号
        contentDetail: pos.contentDetail, // 保留内容详情
        uuid: pos.uuid, // 保存UUID
        isSaved: true // 从服务器加载的数据标记为已保存
      }))
    },

    // 树节点展开事件
    async onTreeExpand(_, { expanded, node }) {
      if (expanded && !node.isLeaf) {
        // 展开时只需要确保3D场景加载了对应的地图，不需要重新构建树形结构
        const mapId = node.key
        if (this.currentMapId !== mapId) {
          await this.loadMap(mapId)
        }
      }
    },

    // 树节点选择事件
    async onTreeSelect(selectedKeys, info) {
      if (selectedKeys.length === 0) {
        // 清空选择，同步取消3D场景中的选中状态
        this.selectObject(null) // 这会同时清空selectedObject和selectedPositionData
        this.currentSelectedMapId = null
        return
      }

      const selectedKey = selectedKeys[0]
      const selectedNode = info.selectedNodes[0]

      if (selectedNode.isLeaf) {
        // 选中的是位置节点
        this.selectedPositionData = { ...selectedNode.objectData } // 复制数据以便编辑

        // 设置当前选中的地图ID
        const { mapId, objectId } = selectedNode
        this.currentSelectedMapId = mapId

        // 确保地图已加载，如果没有加载则先加载
        if (this.currentMapId !== mapId) {
          await this.loadMap(mapId)
        }

        // 在3D场景中选中对应的对象
        // 优先通过uuid查找，其次通过名称查找
        if (selectedNode.objectData.uuid) {
          this.selectObjectByUUID(selectedNode.objectData.uuid)
        } else {
          this.selectObjectByName(selectedNode.objectData.name)
        }
      } else {
        // 选中的是地图节点，取消3D场景中的选中状态
        this.selectObject(null) // 取消3D对象选中
        this.selectedPositionData = null // 清空位置数据选择
        this.currentSelectedMapId = selectedKey // 设置当前选中的地图ID

        // 如果当前显示的不是这个地图，才需要重新加载
        if (this.currentMapId !== selectedKey) {
          await this.loadMap(selectedKey)
        }

        // 自动展开选中的地图节点
        if (!this.expandedKeys.includes(selectedKey)) {
          this.expandedKeys.push(selectedKey)
        }
      }
    },

    // 根据ID在3D场景中选中对象
    selectObjectById(objectId) {
      // 如果objectId为空或者是临时ID，通过名称匹配最新创建的对象
      if (!objectId || objectId.toString().startsWith('temp_')) {
        // 选择最后创建的对象（最新的）
        if (this.sceneObjects.length > 0) {
          const latestObject = this.sceneObjects[this.sceneObjects.length - 1]
          this.selectObject(latestObject)
          console.log(`已选中最新创建的对象: ${latestObject.name}`)
        }
        return
      }

      // 查找对应的3D场景对象
      const targetObject = this.sceneObjects.find(obj => {
        // 支持多种ID匹配方式
        return obj.id.toString() === objectId.toString() ||
          obj.id.toString() === objectId.replace(/\D/g, '') ||
          obj.id === parseInt(objectId.replace(/\D/g, ''))
      })

      if (targetObject) {
        this.selectObject(targetObject)
        console.log(`已在3D场景中选中对象: ${targetObject.name}`)
      } else {
        console.warn(`未找到ID为 ${objectId} 的3D对象`)
      }
    },

    // 根据名称在3D场景中选中对象（用于新建元素）
    selectObjectByName(objectName) {
      const targetObject = this.sceneObjects.find(obj => obj.name === objectName)

      if (targetObject) {
        this.selectObject(targetObject)
        console.log(`已通过名称选中对象: ${targetObject.name}`)
      } else {
        console.warn(`未找到名称为 ${objectName} 的3D对象`)
      }
    },

    // 根据uuid在3D场景中选中对象
    selectObjectByUUID(uuid) {
      if (!uuid) return

      const targetObject = this.sceneObjects.find(obj => obj.uuid === uuid)

      if (targetObject) {
        this.selectObject(targetObject)
        console.log(`已通过uuid选中对象: ${targetObject.name}`)
      } else {
        console.warn(`未找到uuid为 ${uuid} 的3D对象`)
      }
    },

    // 加载地图并将位置点添加为子级
    async loadMapWithPositions(mapId) {
      try {
        // 获取该地图的位置点数据
        const positionData = await this.fetchPositionsByMapId(mapId)

        // 找到选中的地图信息
        const selectedMap = this.mapList.find(map => map.mapId === mapId)
        if (!selectedMap) {
          console.error('未找到指定的地图:', mapId)
          return
        }

        // 重置该地图的位置计数器（基于现有位置数量）
        const existingPositionCount = positionData ? positionData.length : 0
        this.positionCounters[mapId] = existingPositionCount + 1

        // 只显示选中的地图及其位置点，重新构建树形数据
        const mapNode = {
          title: selectedMap.mapName,
          key: selectedMap.mapId,
          isLeaf: false,
          children: []
        }

        // 将位置点数据添加为地图节点的子级
        if (positionData && positionData.length > 0) {
          mapNode.children = positionData.map(pos => ({
            title: pos.name,
            key: `${mapId}_${pos.id}`,
            isLeaf: true,
            mapId: mapId,
            objectId: pos.id,
            objectData: pos
          }))
        }

        // 只显示当前选中的地图
        this.treeData = [mapNode]

        // 自动展开选中的地图节点
        this.expandedKeys = [mapId]

        // 同时加载到3D场景中
        await this.loadMap(mapId)

        console.log(`已加载地图: ${mapId} 及其位置点数据`)
      } catch (error) {
        console.error('加载地图和位置点数据失败:', error)
      }
    },

    // 加载地图数据
    async loadMap(mapId) {
      const mapData = this.mapList.find(map => map.mapId === mapId)
      if (!mapData) return

      this.currentMapId = mapId

      // 清空当前场景
      this.clearSceneWithoutConfirm()

      try {
        // 获取该地图的位置点数据
        const positionData = await this.fetchPositionsByMapId(mapId)

        if (positionData && positionData.length > 0) {
          // 将位置点数据转换为3D场景可用的格式
          const sceneObjects = this.convertPositionDataToSceneObjects(positionData)
          // 导入位置点数据到场景中
          this.importObjects(sceneObjects)
          // 更新nextId（使用数字ID）
          const numericIds = positionData.map(obj => parseInt(obj.id.replace(/\D/g, '')) || 0)
          const maxId = Math.max(...numericIds, this.nextId - 1)
          this.nextId = maxId + 1
        }

        console.log(`已加载地图: ${mapData.mapName}`)
      } catch (error) {
        console.error('加载地图数据失败:', error)
      }
    },



    initThreeJS() {
      // 创建场景
      this.scene = createNonReactiveThreeObject(THREE.Scene)
      this.scene.background = new THREE.Color(0xf0f0f0)

      // 创建相机
      const container = this.$refs.threeContainer
      this.camera = createNonReactiveThreeObject(THREE.PerspectiveCamera,
        75, // 视角
        container.clientWidth / container.clientHeight, // 宽高比
        0.1, // 近平面
        1000 // 远平面
      )
      this.camera.position.set(10, 10, 10)

      // 创建渲染器
      this.renderer = createNonReactiveThreeObject(THREE.WebGLRenderer, { antialias: true })
      this.renderer.setSize(container.clientWidth, container.clientHeight)

      this.renderer.shadowMap.enabled = true
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
      container.appendChild(this.renderer.domElement)

      // 添加控制器
      this.controls = makeThreeObjectNonReactive(new OrbitControls(this.camera, this.renderer.domElement))
      this.controls.enableDamping = true
      this.controls.dampingFactor = 0.05

      // 添加光源
      this.setupLighting()

      // 添加地面
      this.createFloor()

      // 初始化拖拽管理器
      this.dragManager = markRaw(new DragManager(this.scene, this.camera, this.renderer.domElement))
      this.dragManager.setControls(this.controls)
      this.dragManager.setObjectPositionUpdateCallback(this.onDraggedObjectPositionUpdate)

      // 监听窗口大小变化
      window.addEventListener('resize', this.onWindowResize)

      // 监听点击事件
      this.renderer.domElement.addEventListener('click', this.onCanvasClick)

      // 监听鼠标滚轮事件用于调整yaw（使用捕获阶段，优先级更高）
      this.renderer.domElement.addEventListener('wheel', this.onCanvasWheel, {
        passive: false,
        capture: true // 在捕获阶段处理，优先于OrbitControls
      })
    },

    setupLighting() {
      // 环境光
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
      this.scene.add(ambientLight)

      // 方向光
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
      directionalLight.position.set(10, 10, 5)
      directionalLight.castShadow = true
      directionalLight.shadow.mapSize.width = 2048
      directionalLight.shadow.mapSize.height = 2048
      this.scene.add(directionalLight)
    },

    // 创建地板
    createFloor() {
      const geometry = new THREE.PlaneGeometry(30, 30)
      const material = new THREE.MeshLambertMaterial({
        color: 0xcccccc,
        transparent: true,
        opacity: 0.95,
        polygonOffset: true, // 启用多边形偏移，避免与网格共面闪烁
        polygonOffsetFactor: 1,
        polygonOffsetUnits: 1
      })
      const floor = new THREE.Mesh(geometry, material)
      floor.rotation.x = -Math.PI / 2
      floor.receiveShadow = true
      this.scene.add(floor)

      // 添加网格（稍微抬高，避免Z-fighting）
      const gridHelper = new THREE.GridHelper(30, 30, 0x888888, 0xaaaaaa)
      gridHelper.position.y = 0.001
      gridHelper.material.opacity = 0.4
      gridHelper.material.transparent = true
      this.scene.add(gridHelper)

      // 添加坐标轴辅助器（可选，用于调试）
      const axesHelper = new THREE.AxesHelper(5)
      axesHelper.position.y = 0.002
      this.scene.add(axesHelper)
      // 红色=X轴(左右), 绿色=Y轴(上下), 蓝色=Z轴(前后)
    },

    // 设置拖拽元素类型
    startDrag(type, event) {
      event.dataTransfer.setData('text/plain', type)
    },

    // 拖放事件
    setupDragAndDrop() {
      const container = this.$refs.threeContainer

      container.addEventListener('dragover', (e) => {
        e.preventDefault()
      })

      container.addEventListener('drop', (e) => {
        e.preventDefault()
        const objectType = e.dataTransfer.getData('text/plain')
        this.createObjectAtPosition(objectType, e)
      })
    },

    // 创建对象并放置在指定位置
    createObjectAtPosition(type, event) {
      const rect = this.$refs.threeContainer.getBoundingClientRect()
      const mouse = new THREE.Vector2()
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      const raycaster = new THREE.Raycaster()
      raycaster.setFromCamera(mouse, this.camera)

      // 与地面相交
      const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0)
      const intersectPoint = new THREE.Vector3()

      // 检查射线与平面的交点
      const intersection = raycaster.ray.intersectPlane(plane, intersectPoint)

      // 如果没有交点，使用默认位置
      if (!intersection) {
        console.warn('No intersection with ground plane, using default position')
        intersectPoint.set(0, 0, 0)
      }

      // 验证intersectPoint是有效的
      if (!intersectPoint || typeof intersectPoint.x !== 'number' || isNaN(intersectPoint.x)) {
        console.warn('Invalid intersect point, using default position')
        intersectPoint.set(0, 0, 0)
      }

      // 创建对象并自动添加到对应地图的子级
      this.createObjectAndAddToTree(type, intersectPoint)
    },

    // 创建对象并添加到树形结构
    createObjectAndAddToTree(type, position) {
      // 如果没有选中的地图，使用当前加载的地图
      const targetMapId = this.currentSelectedMapId || this.currentMapId

      if (!targetMapId) {
        message.warning('请先打开一个地图')
        return
      }

      // 获取位置计数和名称
      const positionCount = this.getPositionCountForMap(targetMapId)
      const name = `位置${positionCount}`
      const uuid = generateUUID() // 生成严谨的UUID

      // 创建位置数据
      const positionData = {
        name: name,
        xcoordinate: position.x.toString(),
        ycoordinate: position.y.toString(),
        zcoordinate: position.z.toString(),
        yaw: '0',
        actionNo: '',
        contentDetail: '',
        uuid: uuid, // 使用UUID作为唯一标识符
        isSaved: false // 标记为新建，未保存到服务器
      }

      // 创建3D对象
      this.createObject(type, position, {
        name: name,
        actionNo: positionData.actionNo,
        contentDetail: positionData.contentDetail,
        rotation: 0,
        uuid: uuid, // 传递UUID
        isSaved: false // 标记为新建
      })

      // 添加到树形结构
      const newNodeKey = this.addPositionToTree(targetMapId, positionData)

      // 自动选中新添加的位置元素
      this.selectNewlyAddedPosition(newNodeKey, positionData)

      console.log(`已创建位置点并添加到地图 ${targetMapId}: ${name}`)
    },

    // 添加位置点到树形结构
    addPositionToTree(mapId, positionData) {
      const mapNode = this.treeData.find(node => node.key === mapId)
      if (mapNode) {
        if (!mapNode.children) {
          mapNode.children = []
        }

        // 使用uuid作为key的一部分
        const keyId = positionData.uuid
        const newNodeKey = `${mapId}_${keyId}`

        // 添加新的位置点节点
        mapNode.children.push({
          title: positionData.name,
          key: newNodeKey,
          isLeaf: true,
          mapId: mapId,
          objectId: keyId,
          objectData: positionData
        })

        // 触发重新渲染
        this.treeData = [...this.treeData]

        // 确保地图节点展开
        if (!this.expandedKeys.includes(mapId)) {
          this.expandedKeys.push(mapId)
        }

        return newNodeKey
      }
      return null
    },

    // 自动选中新添加的位置元素
    selectNewlyAddedPosition(nodeKey, positionData) {
      if (!nodeKey) return

      // 使用nextTick确保DOM更新完成后再选中
      this.$nextTick(() => {
        // 设置选中的节点
        this.selectedKeys = [nodeKey]

        // 设置位置数据到属性面板
        this.selectedPositionData = { ...positionData }

        // 设置当前选中的地图ID
        const [mapId] = nodeKey.split('_')
        this.currentSelectedMapId = mapId

        // 在3D场景中选中对应的对象
        // 对于新建的位置元素，通过uuid查找
        this.selectObjectByUUID(positionData.uuid)

        console.log(`已自动选中新添加的位置: ${positionData.name}`)
      })
    },

    createObject(type, position, businessData = null) {
      // 验证position参数
      if (!position || typeof position.x !== 'number' || typeof position.y !== 'number' || typeof position.z !== 'number') {
        console.error('createObject: Invalid position parameter:', position)
        position = new THREE.Vector3(0, 0, 0)
      }

      let object
      const id = businessData?.id || this.nextId++
      const name = businessData?.name || `位置${id}`

      try {
        switch (type) {
          case 'person':
            object = makeThreeObjectNonReactive(new Person(id, name, position))
            // 如果有业务数据，保存到对象中
            if (businessData) {
              object.actionNo = businessData.actionNo || ''
              object.contentDetail = businessData.contentDetail || ''
              // 保存UUID标识符
              object.uuid = businessData.uuid || null
              // 保存状态标记
              object.isSaved = businessData.isSaved || false
              if (businessData.rotation !== undefined) {
                object.setRotation(businessData.rotation)
              }
            }
            break
          default:
            console.warn('createObject: Unknown object type:', type)
            return
        }

        if (!object || !object.mesh) {
          console.error('createObject: Failed to create object or mesh')
          return
        }

        this.scene.add(object.mesh)
        this.sceneObjects.push(object)

        // 正确选中新创建的对象（这会取消之前对象的选中状态）
        this.selectObject(object)
      } catch (error) {
        console.error(`Error creating ${type} object:`, error)
      }
    },

    selectObject(object) {
      // 取消之前选中对象的高亮
      if (this.selectedObject && this.selectedObject.setSelected) {
        this.selectedObject.setSelected(false)
      }

      this.selectedObject = object

      // 高亮当前选中对象
      if (object && object.setSelected) {
        object.setSelected(true)
      }

      // 设置位置数据到属性面板
      this.setPositionDataFromObject(object)

      // 同步选中树形结构中对应的节点
      if (object) {
        this.syncTreeSelection(object)
      } else {
        // 取消选中时，清空树形结构的选中状态
        this.selectedKeys = []
      }

      // 强制更新Vue界面
      this.$forceUpdate()
    },

    // 同步选中树形结构中对应的节点
    syncTreeSelection(object) {
      if (!object || !this.currentMapId) return

      const mapNode = this.treeData.find(node => node.key === this.currentMapId)
      if (mapNode && mapNode.children) {
        // 通过uniqueId或名称查找对应的树节点
        const treeNode = mapNode.children.find(child => {
          if (!child.objectData) return false

          // 优先通过uniqueId匹配
          if (object.uniqueId && child.objectData.uniqueId === object.uniqueId) {
            return true
          }

          // 备选：通过名称匹配
          return child.objectData.name === object.name
        })

        if (treeNode) {
          // 选中对应的树节点
          this.selectedKeys = [treeNode.key]
          console.log(`已同步选中树节点: ${treeNode.title}`)
        }
      }
    },

    // 从3D对象设置位置数据（统一属性面板）
    setPositionDataFromObject(object) {
      if (!object) {
        this.selectedPositionData = null
        return
      }

      // 检查是否是新建的元素
      const isNewElement = this.isNewElement(object)

      // 将3D对象数据转换为位置数据格式
      this.selectedPositionData = {
        id: isNewElement ? '' : (object.businessId || ''), // 新建元素ID为空，已保存元素使用businessId
        name: object.name || '未命名位置',
        xcoordinate: (object.position?.x || 0).toString(),
        ycoordinate: (object.position?.y || 0).toString(),
        zcoordinate: (object.position?.z || 0).toString(),
        yaw: object.getRotationDegrees ? object.getRotationDegrees().toString() : '0',
        actionNo: object.actionNo || '',
        contentDetail: object.contentDetail || '',
        uniqueId: object.uniqueId || null // 保存唯一标识符
      }
    },

    // 检查是否是新建的元素
    isNewElement(object) {
      // 简单判断：有uniqueId且没有businessId的是新建元素
      return object.uniqueId && !object.businessId
    },

    // 更新位置点属性
    updatePositionProperty(property, value) {
      if (this.selectedPositionData) {
        // 对于数字类型的属性，确保转换为字符串存储
        if (['xcoordinate', 'ycoordinate', 'zcoordinate'].includes(property)) {
          this.selectedPositionData[property] = String(value || 0)
        } else {
          this.selectedPositionData[property] = value
        }

        // 同步更新树形数据中的objectData
        this.updateTreeNodeObjectData(property, value)

        // 更新3D场景中的对象
        this.updateSceneObjectFromPositionData(property, value)
      }
    },

    // 更新树形数据中的objectData
    updateTreeNodeObjectData(property, value) {
      if (!this.selectedPositionData || !this.currentMapId || !this.selectedKeys.length) return

      const mapNode = this.treeData.find(node => node.key === this.currentMapId)
      if (mapNode && mapNode.children) {
        // 通过当前选中的key来查找树节点
        const selectedKey = this.selectedKeys[0]
        const treeNode = mapNode.children.find(child => child.key === selectedKey)

        if (treeNode && treeNode.objectData) {
          // 更新objectData中的属性
          if (['xcoordinate', 'ycoordinate', 'zcoordinate'].includes(property)) {
            treeNode.objectData[property] = String(value || 0)
          } else {
            treeNode.objectData[property] = value
          }

          // 如果修改的是名称，同时更新树形结构的显示标题
          if (property === 'name') {
            treeNode.title = value
            // 触发树形数据重新渲染
            this.treeData = [...this.treeData]
          }
        }
      }
    },

    // 根据位置数据更新3D场景中的对象
    updateSceneObjectFromPositionData(property, value) {
      if (!this.selectedPositionData || !this.selectedObject) return

      // 直接使用当前选中的3D对象
      const sceneObject = this.selectedObject

      if (sceneObject) {
        // 更新名称
        if (property === 'name') {
          sceneObject.name = value
        }

        // 更新位置坐标
        if (['xcoordinate', 'ycoordinate', 'zcoordinate'].includes(property)) {
          const newPosition = {
            x: parseFloat(this.selectedPositionData.xcoordinate) || 0,
            y: parseFloat(this.selectedPositionData.ycoordinate) || 0,
            z: parseFloat(this.selectedPositionData.zcoordinate) || 0
          }

          sceneObject.position.copy(newPosition)
          if (sceneObject.mesh) {
            sceneObject.mesh.position.copy(newPosition)
          }
        }

        // 更新旋转
        if (property === 'yaw') {
          const newRotation = (parseFloat(this.selectedPositionData.yaw) || 0) * Math.PI / 180
          if (sceneObject.setRotation) {
            sceneObject.setRotation(newRotation)
          }
        }

        // 更新业务属性
        if (property === 'actionNo') {
          sceneObject.actionNo = value || ''
        }

        if (property === 'contentDetail') {
          sceneObject.contentDetail = value || ''
        }

        console.log(`已更新3D对象属性: ${property} = ${value}`)
      }
    },

    // 鼠标点击选中或取消
    onCanvasClick(event) {
      // 将鼠标点击的屏幕坐标转换为Three.js中的标准化设备坐标
      const rect = this.$refs.threeContainer.getBoundingClientRect()
      const mouse = new THREE.Vector2()
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      // 射线投射器
      const raycaster = new THREE.Raycaster()
      raycaster.setFromCamera(mouse, this.camera)

      // 递归检测所有子对象，包括Group内部的mesh
      const intersects = raycaster.intersectObjects(
        this.sceneObjects.map(obj => obj.mesh).filter(mesh => mesh),
        true // 递归检测子对象
      )

      // 查找被点击的对象
      if (intersects.length > 0) {
        const clickedMesh = intersects[0].object

        // 查找对应的场景对象，需要考虑Group结构
        let clickedObject = null

        // 首先尝试直接匹配
        clickedObject = this.sceneObjects.find(obj => obj.mesh === clickedMesh)

        // 如果没找到，可能是Group内部的子对象，向上查找
        if (!clickedObject) {
          let parent = clickedMesh.parent
          while (parent && !clickedObject) {
            clickedObject = this.sceneObjects.find(obj => obj.mesh === parent)
            parent = parent.parent
          }
        }

        // 还可以通过userData查找
        if (!clickedObject) {
          // 检查点击的mesh的userData
          if (clickedMesh.userData && clickedMesh.userData.objectType && clickedMesh.userData.objectId) {
            const { objectType, objectId } = clickedMesh.userData
            clickedObject = this.sceneObjects.find(obj =>
              obj.type === objectType && obj.id === objectId
            )
          }

          // 检查父级的userData
          if (!clickedObject && clickedMesh.parent && clickedMesh.parent.userData) {
            const { objectType, objectId } = clickedMesh.parent.userData
            if (objectType && objectId) {
              clickedObject = this.sceneObjects.find(obj =>
                obj.type === objectType && obj.id === objectId
              )
            }
          }
        }

        if (clickedObject) {
          this.selectObject(clickedObject)
        } else {
          this.selectObject(null) // selectObject(null)已经会清空selectedPositionData和selectedKeys
        }
      } else {
        this.selectObject(null)
      }
    },

    // 鼠标滚轮事件处理
    onCanvasWheel(event) {
      // 只有在选中对象且按住Shift键时才处理滚轮事件
      if (!this.selectedObject || !this.selectedPositionData || !this.isShiftPressed) {
        return // 让OrbitControls处理缩放
      }

      // 完全阻止事件传播，防止OrbitControls处理
      event.preventDefault()
      event.stopPropagation()
      event.stopImmediatePropagation()

      // 计算旋转增量（每次滚动15度）
      const rotationDelta = event.deltaY > 0 ? 15 : -15
      const currentYaw = parseFloat(this.selectedPositionData.yaw) || 0
      let newYaw = currentYaw + rotationDelta

      // 保持角度在0-360范围内
      if (newYaw < 0) newYaw += 360
      if (newYaw >= 360) newYaw -= 360

      // 更新位置数据
      this.selectedPositionData.yaw = newYaw.toString()
      this.updatePositionProperty('yaw', newYaw.toString())

      // 显示临时提示
      this.showRotationHint(newYaw, 'Shift+滚轮调整')

      return false // 确保事件不会继续传播
    },

    // 键盘状态监听（用于跟踪Shift键状态）
    onKeyStateChange(event) {
      this.isShiftPressed = event.shiftKey

      // 当Shift键状态改变时，动态控制OrbitControls的缩放功能
      if (this.controls && this.selectedObject && this.selectedPositionData) {
        if (this.isShiftPressed) {
          // 按下Shift时，如果有选中对象，禁用缩放
          this.controls.enableZoom = false
        } else {
          // 释放Shift时，重新启用缩放
          this.controls.enableZoom = true
        }
      }
    },

    // 键盘事件处理
    onKeyDown(event) {
      // 只有在选中对象时才处理键盘事件
      if (!this.selectedObject || !this.selectedPositionData) {
        return
      }

      // 检查是否在输入框中，如果是则不处理
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return
      }

      let rotationDelta = 0

      switch (event.key) {
        case 'q':
        case 'Q':
          rotationDelta = -15 // 逆时针15度
          break
        case 'e':
        case 'E':
          rotationDelta = 15 // 顺时针15度
          break
        case 'ArrowLeft':
          if (event.ctrlKey) {
            rotationDelta = -15
          }
          break
        case 'ArrowRight':
          if (event.ctrlKey) {
            rotationDelta = 15
          }
          break
        default:
          return // 不处理其他按键
      }

      if (rotationDelta !== 0) {
        event.preventDefault()

        const currentYaw = parseFloat(this.selectedPositionData.yaw) || 0
        let newYaw = currentYaw + rotationDelta

        // 保持角度在0-360范围内
        if (newYaw < 0) newYaw += 360
        if (newYaw >= 360) newYaw -= 360

        // 更新位置数据
        this.selectedPositionData.yaw = newYaw.toString()
        this.updatePositionProperty('yaw', newYaw.toString())

        // 显示提示
        this.showRotationHint(newYaw, `按键: ${event.key.toUpperCase()}`)
      }
    },

    // 显示旋转提示（增强版）
    showRotationHint(yaw, method = 'Shift+滚轮调整') {
      // 创建或更新提示元素
      let hint = document.getElementById('rotation-hint')
      if (!hint) {
        hint = document.createElement('div')
        hint.id = 'rotation-hint'
        hint.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 10px 15px;
          border-radius: 5px;
          font-size: 14px;
          z-index: 1000;
          pointer-events: none;
          transition: opacity 0.3s;
        `
        document.body.appendChild(hint)
      }

      hint.innerHTML = `
        <div>朝向: ${Math.round(yaw)}°</div>
        <div style="font-size: 12px; opacity: 0.8; margin-top: 2px;">${method}</div>
      `
      hint.style.opacity = '1'

      // 2秒后淡出
      clearTimeout(this.hintTimeout)
      this.hintTimeout = setTimeout(() => {
        if (hint) {
          hint.style.opacity = '0'
          setTimeout(() => {
            if (hint && hint.parentNode) {
              hint.parentNode.removeChild(hint)
            }
          }, 300)
        }
      }, 2000)
    },

    onWindowResize() {
      const container = this.$refs.threeContainer
      this.camera.aspect = container.clientWidth / container.clientHeight
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(container.clientWidth, container.clientHeight)
    },

    // 持续更新渲染
    animate() {
      requestAnimationFrame(this.animate)

      // 更新相机控制器
      this.controls.update()

      // 更新所有场景对象
      this.sceneObjects.forEach(obj => {
        if (obj.update) {
          obj.update()
        }
      })

      // 渲染场景
      this.renderer.render(this.scene, this.camera)
    },



    // 拖拽对象位置更新回调
    onDraggedObjectPositionUpdate(objectType, objectId, newPosition) {
      // 找到对应的对象
      const object = this.sceneObjects.find(obj => obj.id === objectId && obj.type === objectType)
      if (!object) {
        return
      }

      // 更新对象的内部位置
      object.position.copy(newPosition)

      // 根据对象类型进行特殊处理
      switch (objectType) {
        case 'person':
          // 人物对象：确保mesh位置同步
          if (object.mesh) {
            object.mesh.position.copy(newPosition)
          }
          break
      }

      // 如果当前选中的是这个对象，触发界面更新
      if (this.selectedObject && this.selectedObject.id === objectId) {
        // 强制Vue更新界面
        this.$forceUpdate()
      }
    },


    // 导入对象
    importObjects(objects) {
      objects.forEach(objData => {
        try {
          this.createObjectFromData(objData)
        } catch (error) {
          console.error(`导入对象失败 (ID: ${objData.id}):`, error)
        }
      })
    },

    // 从数据创建对象
    createObjectFromData(objData) {
      // 创建三维向量的标准方法，表示坐标系原点位置的向量对象
      const position = new THREE.Vector3(objData.position.x, objData.position.y, objData.position.z)

      let object
      switch (objData.type) {
        case 'person':
          object = makeThreeObjectNonReactive(new Person(objData.id, objData.name, position))
          if (objData.rotation !== undefined) {
            object.setRotation(objData.rotation)
          }
          // 保存业务信息到3D对象中
          object.actionNo = objData.actionNo || ''
          object.contentDetail = objData.contentDetail || ''
          // 保存原始业务ID（用于区分新建和已保存元素）
          object.businessId = objData.businessId || objData.id || ''
          break
        default:
          console.warn('未知的对象类型:', objData.type)
          return
      }

      if (object && object.mesh) {
        this.scene.add(object.mesh)
        this.sceneObjects.push(object)
      }
    },

    // 清空场景（不显示确认对话框）
    clearSceneWithoutConfirm() {
      // 移除所有对象
      this.sceneObjects.forEach(obj => {
        this.scene.remove(obj.mesh)
        if (obj.dispose) {
          obj.dispose()
        }
      })

      this.sceneObjects = []
      this.selectedObject = null
    }
  }
}
</script>

<style scoped>
.map3d-container {
  display: flex;
  height: 100vh;
  width: 100%;
}

/* 第一列：地图列表面板 */
.map-list-panel {
  width: 250px;
  background: #fafafa;
  border-right: 1px solid #ddd;
  padding: 20px;
  overflow-y: auto;
}

.map-list-panel h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 5px;
}

/* 第二列：3D容器包装器 */
.three-container-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 操作区 */
.map-operations {
  background: #fff;
  border-bottom: 1px solid #ddd;
  padding: 4px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.map-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 拖动位置元素样式 */
.drag-position-item {
  background: #f0f8ff;
  border: 2px dashed #1890ff;
  border-radius: 6px;
  padding: 6px 30px;
  cursor: grab;
  user-select: none;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
}

.drag-position-item:hover {
  background: #e6f7ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.drag-position-item:active {
  cursor: grabbing;
  transform: translateY(0);
}

/* 第二列：3D容器 */
.three-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 第三列：控制面板 */
.control-panel {
  width: 300px;
  background: #f5f5f5;
  border-left: 1px solid #ddd;
  padding: 20px;
  overflow-y: auto;
}

.control-panel h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}

.object-list {
  margin-bottom: 30px;
}

.object-item {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 8px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.object-item:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.object-item.selected {
  background: #e3f2fd;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.delete-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  transition: background-color 0.2s ease;
}

.delete-btn:hover {
  background: #c82333;
}

.object-properties {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
}

.empty-state {
  height: 100%;
  /* min-height: 300px; */
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.position-actions {
  margin-bottom: 15px;
  padding-bottom: 15px;
  /* border-bottom: 1px solid #eee; */
}

/* 属性行布局优化 */
.property-group {
  margin-bottom: 12px;
}

.property-group.inline {
  display: flex;
  align-items: center;
  gap: 8px;
}

.property-group.block {
  display: block;
}

.property-group.inline label {
  width: 66px;
  margin: 0;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.property-group.inline .ant-input,
.property-group.inline .ant-input-number {
  width: 140px;
}

.property-group.block .ant-input,
.property-group.block .ant-textarea {
  width: 100%;
}

.property-group input,
.property-group .ant-input-number {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  transition: border-color 0.2s ease;
}


.edit-btn,
.add-btn,
.move-btn,
.stop-btn,
.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin: 5px 5px 5px 0;
  transition: background-color 0.2s ease;
}

.edit-btn {
  background: #17a2b8;
  color: white;
}

.edit-btn:hover {
  background: #138496;
}

.add-btn {
  background: #28a745;
  color: white;
}

.add-btn:hover {
  background: #218838;
}

.move-btn {
  background: #007bff;
  color: white;
}

.move-btn:hover {
  background: #0056b3;
}

.move-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.stop-btn {
  background: #dc3545;
  color: white;
}

.stop-btn:hover {
  background: #c82333;
}

.action-btn {
  background: #6c757d;
  color: white;
  width: 100%;
  margin-bottom: 10px;
}

.action-btn:hover {
  background: #545b62;
}

.action-btn.danger {
  background: #dc3545;
}

.action-btn.danger:hover {
  background: #c82333;
}

.action-btn.export {
  background: #28a745;
}

.action-btn.export:hover {
  background: #218838;
}

.action-btn.import {
  background: #17a2b8;
}

.action-btn.import:hover {
  background: #138496;
}
</style>