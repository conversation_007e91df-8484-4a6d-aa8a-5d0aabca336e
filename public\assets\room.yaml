# 房间地图配置文件
# 地图图像文件名 - 指定包含地图数据的图像文件
image: room.pgm

# 地图模式 - 定义如何解释图像中的像素值
# trinary: 三值模式，像素被分类为占用(障碍物)、空闲(可通行)或未知(未探索)
mode: trinary

# 地图分辨率 - 每个像素代表的实际距离（米/像素）
# 0.05表示每个像素代表5厘米，适合室内房间地图
resolution: 0.05

# 地图原点 - 地图左下角在世界坐标系中的位置 [x, y, yaw]
# [-6.0, -4.0, 0] 表示原点位于x=-6.0米, y=-4.0米, 旋转角度=0度
origin: [-6.0, -4.0, 0]

# 是否反转占用逻辑 - 0表示不反转
# 0: 白色=空闲空间，黑色=占用空间（墙壁、家具等）
# 1: 黑色=空闲空间，白色=占用空间
negate: 0

# 占用阈值 - 像素值超过此阈值被认为是占用的(用于区分障碍物和空闲空间)
# 0.65表示灰度值大于65%的像素被标记为占用（墙壁、家具）
occupied_thresh: 0.65

# 空闲阈值 - 像素值低于此阈值被认为是空闲的
# 0.25表示灰度值小于25%的像素被标记为空闲（可行走区域）
free_thresh: 0.25

# 房间信息注释
# 房间尺寸: 12米 x 8米 (240像素 x 160像素，分辨率0.05m/pixel)
# 包含: 客厅、厨房、卧室、卫生间
# 家具: 沙发、桌子、床、橱柜等
# 门窗: 入户门、房间门、窗户
