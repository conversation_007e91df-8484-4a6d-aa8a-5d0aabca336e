<template>
  <div id="app">
    <Map3D />
  </div>
</template>

<script>
import Map3D from './components/Map3D.vue'

export default {
  name: 'App',
  components: {
    Map3D
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.app-header h1 {
  font-size: 28px;
  margin-bottom: 8px;
  font-weight: 600;
}

.app-header p {
  font-size: 16px;
  opacity: 0.9;
  font-weight: 300;
}

body {
  margin: 0;
  overflow: hidden;
}
</style>
