# 室内3D地图路线系统

基于Vue 3和Three.js构建的交互式室内3D地图路线系统，支持人物、路线和障碍物的创建与管理。

## 功能特性

### 🎯 核心功能
- **拖拽创建**: 从左侧面板拖拽元素到3D场景中创建对象
- **实时编辑**: 选中对象后可在右侧面板编辑属性
- **路径规划**: 创建路线并让人物沿路径移动
- **碰撞检测**: 障碍物支持碰撞检测功能
- **CRUD操作**: 完整的增删改查功能

### 📦 支持的对象类型

#### 👤 人物 (Person)
- 3D人物模型（头部、身体、四肢）
- 沿路径移动动画
- 可调节移动速度
- 走路动画效果

#### 📍 路线 (Path)
- 平滑曲线路径
- 可编辑控制点
- 方向箭头指示
- 自定义颜色和线宽

#### 🧱 障碍物 (Obstacle)
- 长方体障碍物
- 可调节尺寸（宽度、高度、深度）
- 碰撞检测支持
- 阴影效果

## 使用方法

### 基本操作

1. **创建对象**
   - 从左侧拖拽元素到3D场景
   - 对象会在鼠标释放位置创建

2. **选择对象**
   - 点击3D场景中的对象
   - 或点击右侧对象列表中的项目

3. **编辑属性**
   - 选中对象后在右侧属性面板编辑
   - 支持位置、尺寸、颜色等属性

4. **删除对象**
   - 点击对象列表中的"×"按钮
   - 或选中对象后使用删除功能

### 高级功能

#### 路径编辑
1. 选择路线对象
2. 点击"编辑路径"按钮
3. 拖拽控制点调整路径
4. 点击"添加点"增加路径节点
5. 点击"完成编辑"结束编辑

#### 人物移动
1. 创建至少一条路线
2. 选择人物对象
3. 点击"沿路径移动"按钮
4. 人物将自动沿最近路径移动

#### 场景管理
- **复制对象**: 选中对象后点击"复制对象"
- **清空场景**: 点击"清空场景"删除所有对象

## 控制说明

### 3D场景控制
- **旋转视角**: 鼠标左键拖拽
- **缩放**: 鼠标滚轮
- **平移**: 鼠标右键拖拽

### 对象操作
- **选择**: 左键点击对象
- **拖拽**: 选中后拖拽移动（需要对象支持拖拽）
- **编辑**: 在右侧属性面板修改参数

## 技术架构

### 核心技术栈
- **Vue 3**: 前端框架
- **Three.js**: 3D图形渲染
- **OrbitControls**: 3D场景控制

### 主要组件
- `Map3D.vue`: 主要3D地图组件
- `Person.js`: 人物对象类
- `Path.js`: 路线对象类
- `Obstacle.js`: 障碍物对象类
- `DragManager.js`: 拖拽管理器

### 文件结构
```
src/
├── components/
│   └── Map3D.vue          # 主要3D地图组件
├── classes/
│   ├── Person.js          # 人物类
│   ├── Path.js            # 路线类
│   └── Obstacle.js        # 障碍物类
├── managers/
│   └── DragManager.js     # 拖拽管理器
└── App.vue                # 主应用组件
```

## 开发说明

### 启动项目
```bash
npm run serve
```

### 扩展功能
系统采用模块化设计，可以轻松扩展：

1. **添加新对象类型**: 在`classes/`目录下创建新类
2. **扩展属性**: 在对象类中添加新属性和更新方法
3. **增强交互**: 在`DragManager.js`中添加新的交互逻辑

### 性能优化
- 使用对象池管理大量对象
- 实现LOD（细节层次）系统
- 优化渲染循环和动画

## 注意事项

1. **浏览器兼容性**: 需要支持WebGL的现代浏览器
2. **性能考虑**: 大量对象时可能影响性能
3. **内存管理**: 删除对象时会自动清理资源

## 未来改进

- [ ] 添加更多对象类型（门、窗、家具等）
- [ ] 实现路径寻找算法
- [ ] 支持多层建筑
- [ ] 添加物理引擎
- [ ] 支持导入/导出场景
- [ ] 添加动画时间轴
- [ ] 实现协作编辑功能
