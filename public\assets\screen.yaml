# 地图图像文件名 - 指定包含地图数据的图像文件
image: screen.pgm

# 地图模式 - 定义如何解释图像中的像素值
# trinary: 三值模式，像素被分类为占用(障碍物)、空闲(可通行)或未知(未探索)
mode: trinary

# 地图分辨率 - 每个像素代表的实际距离（米/像素）
# 0.025表示每个像素代表2.5厘米
resolution: 0.025

# 地图原点 - 地图左下角在世界坐标系中的位置 [x, y, yaw]
# [-11.4, -8.63, 0] 表示原点位于x=-11.4米, y=-8.63米, 旋转角度=0度
origin: [-11.4, -8.63, 0]

# 是否反转占用逻辑 - 0表示不反转
# 0: 白色=空闲空间，黑色=占用空间
# 1: 黑色=空闲空间，白色=占用空间
negate: 0

# 占用阈值 - 像素值超过此阈值被认为是占用的(用于区分障碍物和空闲空间)
# 0.65表示灰度值大于65%的像素被标记为占用
occupied_thresh: 0.65

# 空闲阈值 - 像素值低于此阈值被认为是空闲的
# 0.25表示灰度值小于25%的像素被标记为空闲
free_thresh: 0.25