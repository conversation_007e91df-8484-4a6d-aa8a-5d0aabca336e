<template>
  <div class="map3d-container">
    <!-- 第一列：位置列表 -->
    <div class="map-list-panel">
      <h3>位置列表</h3>

      <!-- 如果没有选择地图，显示空状态 -->
      <a-empty v-if="!currentMapId" description="请打开地图以展示位置" :image="false">
        <template #description>
          <span style="color: #999;">请打开地图以展示位置</span>
        </template>
      </a-empty>

      <!-- 如果已选择地图，显示树形结构 -->
      <a-tree v-else v-model:expandedKeys="expandedKeys" v-model:selectedKeys="selectedKeys" :load-data="onLoadData"
        :tree-data="treeData" @select="onTreeSelect" @expand="onTreeExpand">
        <template #title="{ title }">
          <span>{{ title }}</span>
        </template>
      </a-tree>
    </div>

    <!-- 第二列：3D容器 -->
    <div class="three-container-wrapper">
      <!-- 操作区 -->
      <div class="map-operations">
        <div class="map-buttons">
          <a-button type="primary" @click="showMapSelectModal">
            打开地图
          </a-button>
          <a-button type="default" @click="saveMap" :disabled="!currentMapId">
            保存地图
          </a-button>
          <a-button type="default" danger @click="deleteMap" :disabled="!currentMapId">
            删除地图
          </a-button>
          <a-button type="default" @click="showFileManageModal" :disabled="!currentMapId">
            文件管理
          </a-button>
          <a-button :type="isPCDRendered ? 'default' : 'primary'" @click="togglePCD"
            :disabled="!currentMapId || !currentMapData || !currentMapData.pcdName">
            {{ isPCDRendered ? '清除PCD' : '渲染PCD' }}
          </a-button>
          <a-button type="default" @click="togglePerformancePanel" :disabled="!renderer">
            性能监控
          </a-button>
          <a-button type="default" @click="optimizePerformance" :disabled="!renderer">
            性能优化
          </a-button>
        </div>

        <!-- 拖动位置元素 -->
        <div class="drag-position-item" draggable="true" @dragstart="startDrag('person', $event)">
          <span>👤 位置</span>
        </div>
      </div>

      <!-- 3D容器 -->
      <div ref="threeContainer" class="three-container"></div>
    </div>

    <!-- 地图选择模态框 -->
    <a-modal v-model:visible="mapSelectModalVisible" title="选择地图" :maskClosable="false" @ok="handleMapSelect"
      @cancel="handleMapSelectCancel" okText="打开" cancelText="取消">
      <a-select v-model:value="selectedMapId" placeholder="请选择地图" style="width: 100%;">
        <a-select-option v-for="map in mapList" :key="map.id" :value="map.id">
          {{ map.name }}
        </a-select-option>
      </a-select>
    </a-modal>

    <!-- 保存地图模态框 -->
    <a-modal v-model:visible="saveMapModalVisible" title="保存地图" :maskClosable="false" @ok="handleSaveMapConfirm"
      @cancel="handleSaveMapCancel" okText="保存" cancelText="取消">
      <div class="save-map-form">
        <a-input id="mapName" v-model:value="editableMapName" placeholder="请输入地图名称"
          style="width: 100%; margin-top: 8px;" />
      </div>
    </a-modal>

    <!-- 文件管理模态框组件 -->
    <FileManageModal v-model:visible="fileManageModalVisible" :current-map-id="currentMapId"
      :current-map-data="currentMapData" @files-uploaded="handleFilesUploaded" />

    <!-- 第三列：属性编辑 -->
    <div class="control-panel">
      <!-- 性能监控面板组件 -->
      <PerformancePanel v-model:visible="showPerformancePanel" :renderer="renderer" :pcd-renderer="pcdRenderer"
        :pgm-renderer="pgmRenderer" :current-map-id="currentMapId" :current-map-data="currentMapData"
        :performance-stats="performanceStats" @optimize-performance="optimizePerformance" />

      <!-- 未选中时的空状态 -->
      <a-empty v-if="!selectedPositionData" class="empty-state" description="请选择位置元素以编辑属性" :image="false">
        <template #description>
          <span style="color: #999;">请选择位置元素以编辑属性</span>
        </template>
      </a-empty>

      <!-- 选中时的属性编辑 -->
      <div v-if="selectedPositionData" class="object-properties">

        <h3>属性编辑</h3>

        <div class="property-group inline">
          <label>位置ID:</label>
          <a-input v-model:value="selectedPositionData.uuid" disabled size="small" />
        </div>
        <div class="property-group inline">
          <label>位置名称:</label>
          <a-input v-model:value="selectedPositionData.name"
            @change="e => updatePositionProperty('name', e.target.value)" size="small" />
        </div>
        <div class="property-group inline">
          <label>X坐标:</label>
          <a-input-number v-model:value="selectedPositionData.xcoordinate" :min="coordinateBounds.minX"
            :max="coordinateBounds.maxX" :step="0.01" :precision="2"
            @change="val => updatePositionProperty('xcoordinate', val)" size="small" />
        </div>
        <div class="property-group inline">
          <label>Y坐标:</label>
          <a-input-number v-model:value="selectedPositionData.ycoordinate" :min="coordinateBounds.minY"
            :max="coordinateBounds.maxY" :step="0.01" :precision="2"
            @change="val => updatePositionProperty('ycoordinate', val)" size="small" />
        </div>
        <div class="property-group inline">
          <label>Z坐标:</label>
          <a-input-number v-model:value="selectedPositionData.zcoordinate" :min="coordinateBounds.minZ"
            :max="coordinateBounds.maxZ" :step="0.01" :precision="2"
            @change="val => updatePositionProperty('zcoordinate', val)" size="small" />
        </div>
        <div class="property-group inline">
          <a-tooltip placement="top">
            <template #title>
              <div>
                0°=正面朝前, 90°=朝右, 180°=朝后, 270°=朝左<br>
                交互方式：Shift+滚轮 | 拖拽圆盘 | Q/E | Ctrl+左右箭头
              </div>
            </template>
            <label style="cursor: help;">Yaw(°):</label>
          </a-tooltip>
          <a-input-number :min="0" :max="360" :step="15" :value="Number(selectedPositionData.yaw || 0)"
            @change="val => updatePositionProperty('yaw', String(val))" size="small" />
        </div>
        <div class="property-group inline">
          <label>动作编号:</label>
          <a-input v-model:value="selectedPositionData.actionNo"
            @change="e => updatePositionProperty('actionNo', e.target.value)" size="small" />
        </div>
        <div class="property-group block">
          <label>内容详情:</label>
          <a-textarea v-model:value="selectedPositionData.contentDetail"
            @change="e => updatePositionProperty('contentDetail', e.target.value)" :rows="10" size="small" />
        </div>
        <!-- 操作按钮 -->
        <div class="position-actions">
          <a-button block danger @click="deleteSelectedPosition">
            删除位置
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three'
import { Person } from '../classes/Person.js'
import { DragManager } from '../managers/DragManager.js'
import { markRaw } from 'vue'

import FileManageModal from './FileManageModal.vue'
import PerformancePanel from './PerformancePanel.vue'
import { makeThreeObjectNonReactive } from '../utils/threeUtils.js'
import { message, Modal } from 'ant-design-vue'

// 导入配置和工具模块
import { MapConfig } from '../config/mapConfig.js'
import { PGMRenderer } from '../utils/pgmRenderer.js'
import { PCDRenderer } from '../utils/pcdRenderer.js'
import { TreeDataManager } from '../utils/treeDataManager.js'
import { PositionManager } from '../utils/positionManager.js'
import { SceneSetup } from '../utils/sceneSetup.js'
import { performanceMonitor } from '../utils/performanceMonitor.js'
import { renderOptimizer } from '../utils/renderOptimizer.js'

import { deletePosition } from '../api/position.js'
import { requestSuccess, requestFailed, showLoading, hideLoading, generateUUID } from '../utils/common.js'
import { getMapList, getMapInfo, downloadMapFile, saveMapInfo, deleteMapById } from '../api/map.js'

export default {
  name: 'Map3D',
  components: {
    FileManageModal,
    PerformancePanel
  },
  data() {
    return {
      scene: null, // 场景
      camera: null, // 相机
      renderer: null, // 渲染器
      controls: null, // 控制器
      dragManager: null, // 拖拽管理器
      sceneObjects: [], // 场景对象
      selectedObject: null, // 选中的对象
      selectedPositionData: null, // 选中的位置点数据（统一属性面板）
      // 当前选中的地图ID（用于拖拽时确定插入位置）
      currentSelectedMapId: null,
      // 旋转相关
      hintTimeout: null,
      // 键盘状态跟踪
      isShiftPressed: false,
      // 树形数据相关
      treeData: [],
      expandedKeys: [],
      selectedKeys: [],
      // 地图数据
      mapList: [],
      currentMapId: null,
      currentMapData: null, // 当前地图的详细数据
      // 地图选择模态框相关
      mapSelectModalVisible: false,
      selectedMapId: null,
      // 保存地图模态框相关
      saveMapModalVisible: false,
      editableMapName: '',
      // 文件管理模态框相关
      fileManageModalVisible: false,
      // 存储上传后的新文件名
      uploadedFileNames: {
        pgm: null,
        pcd: null,
        yaml: null
      },
      // PCD渲染状态
      isPCDRendered: false,
      // 工具类实例
      pgmRenderer: null,
      pcdRenderer: null,
      treeDataManager: null,
      positionManager: null,

      // 性能监控相关
      performanceStats: {
        fps: 0,
        memoryMB: 0,
        points: 0
      },
      showPerformancePanel: false
    }
  },

  mounted() {
    // 初始化工具类
    this.initializeManagers()

    // 初始化Three.js场景
    this.initThreeJS()
    this.setupDragAndDrop()
    this.animate()

    // 初始化性能监控
    this.initPerformanceMonitoring()

    // 添加键盘事件监听
    document.addEventListener('keydown', this.onKeyDown)
    document.addEventListener('keydown', this.onKeyStateChange)
    document.addEventListener('keyup', this.onKeyStateChange)

    // 获取地图列表
    this.loadMapList();
  },
  beforeUnmount() {
    // 停止性能监控
    performanceMonitor.stop()

    if (this.renderer) {
      this.renderer.dispose()
    }

    // 清理渲染器资源
    if (this.pgmRenderer) {
      this.pgmRenderer.dispose()
    }
    if (this.pcdRenderer) {
      this.pcdRenderer.dispose()
    }

    // 清理事件监听
    document.removeEventListener('keydown', this.onKeyDown)
    document.removeEventListener('keydown', this.onKeyStateChange)
    document.removeEventListener('keyup', this.onKeyStateChange)
  },
  computed: {
    // 计算当前的坐标边界限制
    coordinateBounds() {
      // 优先使用PGM边界
      if (this.pgmRenderer && this.pgmRenderer.hasPGM()) {
        const bounds = this.pgmRenderer.getPGMBounds()
        if (bounds) {
          return {
            minX: Math.round(bounds.minX * 100) / 100, // 保留2位小数
            maxX: Math.round(bounds.maxX * 100) / 100,
            minZ: Math.round(bounds.minZ * 100) / 100,
            maxZ: Math.round(bounds.maxZ * 100) / 100,
            minY: 0,
            maxY: 10 // Y轴最大值设为10
          }
        }
      }

      // 其次使用PCD边界
      if (this.pcdRenderer && this.pcdRenderer.hasPCD()) {
        const bounds = this.pcdRenderer.getPCDBounds()
        if (bounds) {
          return {
            minX: Math.round(bounds.minX * 100) / 100,
            maxX: Math.round(bounds.maxX * 100) / 100,
            minZ: Math.round(bounds.minZ * 100) / 100,
            maxZ: Math.round(bounds.maxZ * 100) / 100,
            minY: Math.round(bounds.minY * 100) / 100,
            maxY: Math.round(bounds.maxY * 100) / 100
          }
        }
      }

      // 默认边界
      return {
        minX: -10,
        maxX: 10,
        minZ: -10,
        maxZ: 10,
        minY: 0,
        maxY: 10
      }
    }
  },
  methods: {
    // 生成UUID
    generateUUID() {
      return generateUUID()
    },

    // 初始化管理器
    initializeManagers() {
      // 初始化工具类
      this.treeDataManager = new TreeDataManager()
      this.positionManager = new PositionManager()

      // 初始化空的地图数据，等待接口返回
      this.mapList = []
      this.treeData = []

      // 同步树形数据状态
      this.expandedKeys = []
      this.selectedKeys = []
    },

    // 加载地图列表
    async loadMapList() {
      try {
        const res = await getMapList()
        if (res.errorCode === 0) {
          this.mapList = res.datas || []
        } else {
          requestSuccess(res)
        }
      } catch (err) {
        requestFailed(err)
      }
    },
    // 显示地图选择模态框
    showMapSelectModal() {
      this.mapSelectModalVisible = true
    },

    // 处理地图选择确认
    async handleMapSelect() {
      if (this.selectedMapId) {
        try {
          // 调用 getMapInfo 接口获取地图详细信息
          const res = await getMapInfo(this.selectedMapId)
          if (res.errorCode === 0) {
            // 处理地图信息和位置信息
            this.mapSelectModalVisible = false
            this.uploadedFileNames = {
              pgm: null,
              pcd: null,
              yaml: null
            }
            await this.processMapInfo(res.data)
          } else {
            requestSuccess(res)
          }
        } catch (err) {
          requestFailed(err)
        }
      }
    },

    // 处理地图选择取消
    handleMapSelectCancel() {
      this.mapSelectModalVisible = false
    },

    // 处理地图信息，构建树形结构
    async processMapInfo(mapData) {
      if (!mapData) return

      console.log(`开始处理地图信息: ${mapData.name}`)

      // 清空当前场景（包括PCD和PGM）
      this.clearSceneWithoutConfirm()

      // 设置当前地图ID和数据
      this.currentMapId = mapData.id
      this.currentMapData = mapData

      // 构建树形结构数据
      const treeNode = {
        title: mapData.name,
        key: mapData.id.toString(),
        isLeaf: false,
        children: []
      }

      // 处理位置信息
      if (mapData.positionInformations && mapData.positionInformations.length > 0) {
        // 将位置信息转换为子节点，并为每个位置创建UUID
        const enhancedPositions = mapData.positionInformations.map(position => {
          // 如果已有uuid则使用，否则生成新的uuid
          const uuid = position.uuid || this.generateUUID()
          return {
            ...position,
            uuid: uuid
          }
        })

        enhancedPositions.forEach(position => {
          const positionNode = {
            title: position.name,
            key: `${mapData.id}_${position.uuid}`,
            isLeaf: true,
            mapId: mapData.id,
            objectId: position.uuid,
            objectData: {
              name: position.name,
              xcoordinate: position.xcoordinate || '0',
              ycoordinate: position.ycoordinate || '0',
              zcoordinate: position.zcoordinate || '0',
              yaw: position.yaw || '0',
              actionNo: position.actionNo || '',
              contentDetail: position.contentDetail || '',
              mapId: position.mapId || mapData.id.toString(),
              uuid: position.uuid,
              isSaved: true // 从服务器加载的数据标记为已保存
            }
          }
          treeNode.children.push(positionNode)
        })

        // 将位置信息转换为3D场景对象，传递uuid
        const sceneObjects = this.convertPositionDataToSceneObjects(enhancedPositions)
        this.importObjects(sceneObjects)
      }

      // 更新树形数据
      this.treeData = [treeNode]
      this.expandedKeys = [mapData.id.toString()]
      this.selectedKeys = []

      // 同步到 TreeDataManager
      this.treeDataManager.setTreeData(this.treeData)
      this.treeDataManager.setExpandedKeys(this.expandedKeys)
      this.treeDataManager.setSelectedKeys(this.selectedKeys)

      // 更新位置计数器，确保新建位置不会与已有位置重名
      this.updatePositionCounterFromExistingPositions(mapData.id, mapData.positionInformations || [])

      // 如果有PGM文件，下载并渲染
      if (mapData.pgmName) {
        await this.downloadAndRenderPGM(mapData.id, mapData.pgmName, mapData.yamlName)
      }
    },

    // 下载并渲染PGM文件
    async downloadAndRenderPGM(mapId, pgmName, yamlName) {

      showLoading('正在渲染PGM地图...')
      try {

        // 调用下载接口获取PGM文件
        const pgmResponse = await downloadMapFile(mapId, pgmName)

        if (pgmResponse) {
          // 接口返回的直接就是blob数据，创建URL
          const pgmUrl = URL.createObjectURL(pgmResponse)

          let yamlUrl = null

          try {
            const yamlResponse = await downloadMapFile(mapId, yamlName)
            if (yamlResponse) {
              yamlUrl = URL.createObjectURL(yamlResponse)
            }
          } catch (yamlError) {
            console.warn('YAML文件下载失败，将使用默认配置:', yamlError)
          }

          // 使用PGMRenderer渲染PGM文件
          const result = await this.pgmRenderer.renderPGM(pgmUrl, yamlUrl)

          // 隐藏默认地板和网格
          SceneSetup.hideDefaultFloor(this.scene)

          // 设置拖拽管理器的边界
          this.updateDragManagerBounds(result.bounds)

          // 调整相机位置以适应PGM地图
          this.adjustCameraForPGM(result.bounds)

          // 限制现有位置元素在PGM范围内
          this.clampExistingPositionsToPGM()

          // 更新现有位置元素的缩放比例
          this.updateExistingPersonScale()

          // 性能优化：根据地图复杂度调整拖拽管理器性能设置
          if (this.dragManager && result.data) {
            const mapComplexity = result.data.width * result.data.height
            this.dragManager.autoAdjustPerformance(mapComplexity)
            this.dragManager.updateDraggableObjectsCache(this.sceneObjects)
          }

          // 清理临时URL
          URL.revokeObjectURL(pgmUrl)
          if (yamlUrl) {
            URL.revokeObjectURL(yamlUrl)
          }

          hideLoading()
          message.success('PGM地图渲染成功')
          console.log('PGM文件渲染完成:', result.data)
        } else {
          hideLoading()
          message.error('PGM渲染失败')
        }
      } catch (error) {
        hideLoading()
        message.error('PGM文件处理失败: ' + error.message)
        console.error('PGM文件处理错误:', error)
      }
    },

    // 显示保存地图模态框
    saveMap() {
      if (!this.currentMapId) {
        message.warning('请先选择一个地图')
        return
      }

      if (!this.currentMapData) {
        message.warning('当前地图数据不完整')
        return
      }

      // 设置可编辑的地图名称为当前地图名称
      this.editableMapName = this.currentMapData.name || ''
      // 显示保存地图模态框
      this.saveMapModalVisible = true
    },

    // 处理保存地图确认
    async handleSaveMapConfirm() {
      try {
        showLoading('正在保存地图信息...')

        // 收集当前地图的所有位置点信息
        const positionList = this.collectCurrentMapPositions()

        // 构建参数，如果有上传后的新文件名则使用新文件名，否则使用原文件名
        const params = {
          id: this.currentMapId,
          name: this.editableMapName, // 使用编辑后的地图名称
          pgmName: this.uploadedFileNames.pgm || this.currentMapData.pgmName,
          pcdName: this.uploadedFileNames.pcd || this.currentMapData.pcdName,
          yamlName: this.uploadedFileNames.yaml || this.currentMapData.yamlName,
          positionInformationRequests: positionList
        }

        console.log('准备发送的参数:', JSON.stringify(params, null, 2))

        const res = await saveMapInfo(params)

        hideLoading()

        if (res.errorCode === 0) {
          message.success('地图保存成功')

          // 将所有新建的位置元素标记为已保存
          this.markAllPositionsAsSaved()

          // 检查地图名称是否发生了变化
          const nameChanged = this.currentMapData.name !== this.editableMapName

          // 检查是否有新文件上传
          const hasNewFiles = this.uploadedFileNames.pgm || this.uploadedFileNames.pcd || this.uploadedFileNames.yaml

          // 更新当前地图数据中的名称和文件名
          this.currentMapData.name = this.editableMapName
          if (this.uploadedFileNames.pgm) {
            this.currentMapData.pgmName = this.uploadedFileNames.pgm
          }
          if (this.uploadedFileNames.pcd) {
            this.currentMapData.pcdName = this.uploadedFileNames.pcd
          }
          if (this.uploadedFileNames.yaml) {
            this.currentMapData.yamlName = this.uploadedFileNames.yaml
          }

          // 如果地图名称发生了变化，同步更新树形结构中的地图名称
          if (nameChanged) {
            this.updateTreeMapName(this.currentMapId, this.editableMapName)
          }

          // 注意：文件重新渲染已经在关闭文件管理模态框时完成，这里只需要更新文件名
          if (hasNewFiles) {
            console.log('保存地图时更新文件名，重新渲染已在文件管理模态框关闭时完成')
          }

          // 清空上传的文件名记录
          this.uploadedFileNames = {
            pgm: null,
            pcd: null,
            yaml: null
          }

          // 重新加载地图列表以确保数据同步
          await this.loadMapList()

          // 关闭模态框
          this.saveMapModalVisible = false
        } else {
          requestSuccess(res)
        }

      } catch (error) {
        hideLoading()
        requestFailed(error)
      }
    },

    // 处理保存地图取消
    handleSaveMapCancel() {
      this.saveMapModalVisible = false
      this.editableMapName = ''
    },

    // 将所有位置元素标记为已保存
    markAllPositionsAsSaved() {
      // 更新3D场景中的所有对象
      this.sceneObjects.forEach(obj => {
        if (!obj.isSaved) {
          obj.isSaved = true
        }
      })

      // 更新树形结构中的所有位置数据
      this.treeData.forEach(mapNode => {
        if (mapNode.children) {
          mapNode.children.forEach(positionNode => {
            if (positionNode.objectData && !positionNode.objectData.isSaved) {
              positionNode.objectData.isSaved = true
            }
          })
        }
      })

      // 如果当前有选中的位置数据，也更新它
      if (this.selectedPositionData && !this.selectedPositionData.isSaved) {
        this.selectedPositionData.isSaved = true
      }

      console.log('所有位置元素已标记为已保存')
    },

    // 更新树形结构中的地图名称
    updateTreeMapName(mapId, newName) {
      if (!mapId || !newName) return

      // 使用 TreeDataManager 更新地图名称
      const updated = this.treeDataManager.updateMapName(mapId, newName)

      if (updated) {
        // 同步更新本地树形数据
        this.treeData = this.treeDataManager.getTreeData()

      } else {
        console.warn(`未找到地图 ${mapId}，无法更新名称`)
      }
    },

    // 重新加载当前地图（用于文件更新后重新渲染）
    async reloadCurrentMap() {
      if (!this.currentMapId || !this.currentMapData) {
        console.warn('没有当前地图可以重新加载')
        return
      }
      showLoading('地图文件更新，正在重新渲染PGM地图...')
      try {
        // 创建更新后的地图数据，使用最新的文件名组合
        const updatedMapData = {
          ...this.currentMapData,
          // 如果有新上传的文件就用新的，没有就用原来的
          pgmName: this.uploadedFileNames.pgm || this.currentMapData.pgmName,
          yamlName: this.uploadedFileNames.yaml || this.currentMapData.yamlName,
          pcdName: this.uploadedFileNames.pcd || this.currentMapData.pcdName
        }

        console.log('使用更新后的文件名重新渲染地图:', {
          pgm: updatedMapData.pgmName,
          yaml: updatedMapData.yamlName,
          pcd: updatedMapData.pcdName
        })

        // 重新处理地图信息，使用更新后的文件名
        await this.processMapInfo(updatedMapData)

        hideLoading()
      } catch (error) {
        hideLoading()
        console.error('重新加载当前地图失败:', error)
        message.error('重新加载地图失败: ' + error.message)
      }
    },

    // 显示文件管理模态框
    showFileManageModal() {
      if (!this.currentMapId) {
        message.warning('请先选择一个地图')
        return
      }

      this.fileManageModalVisible = true
    },

    // 处理文件上传完成事件
    async handleFilesUploaded(uploadedFileNames) {
      console.log('检测到文件上传，重新渲染地图')

      // 更新上传的文件名
      Object.assign(this.uploadedFileNames, uploadedFileNames)

      // 立即更新 currentMapData 中对应的文件名
      if (uploadedFileNames.pgm) {
        this.currentMapData.pgmName = uploadedFileNames.pgm
      }
      if (uploadedFileNames.pcd) {
        this.currentMapData.pcdName = uploadedFileNames.pcd
      }
      if (uploadedFileNames.yaml) {
        this.currentMapData.yamlName = uploadedFileNames.yaml
      }

      // 重新渲染地图
      await this.reloadCurrentMap()
    },



    // 收集当前地图的所有位置点信息
    collectCurrentMapPositions() {
      return MapConfig.collectMapPositions(this.treeData, this.currentMapId, this.sceneObjects)
    },

    // 根据地图ID删除地图
    async deleteMap() {
      if (this.selectedMapId) {
        Modal.confirm({
          title: `你确定要删除地图"${this.currentMapData.name}"吗？`,
          content: '如果是的话请点击【确认按钮】，否则请点击【取消按钮】',
          okText: '确认',
          okType: 'danger',
          cancelText: '取消',
          async onOk() {
            showLoading('正在删除地图...')
            try {
              const res = await deleteMapById(this.selectedMapId)
              if (res.errorCode === 0) {
                message.success('地图删除成功')
              } else {
                requestSuccess(res)
              }
              hideLoading()
            } catch (err) {
              hideLoading()
              requestFailed(err)
            }
          },
          onCancel() {
            console.log('Cancel');
          },
        });



      } else {
        message.warning('请先选择一个地图')
      }
    },

    // 删除选中的位置
    deleteSelectedPosition() {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除位置"${this.selectedPositionData.name}"吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.performDeletePosition()
        }
      })
    },

    // 执行删除位置操作
    async performDeletePosition() {
      if (!this.selectedPositionData) return

      // 判断是否为新建位置（通过检查isSaved状态来判断）
      const isNewPosition = !this.selectedObject || !this.selectedObject.isSaved

      if (isNewPosition) {
        // 新建位置，直接执行本地删除操作，不需要调用接口
        this.performLocalDelete()
        message.success('位置删除成功')
      } else {
        // 已保存的位置，需要先调用删除接口
        try {
          showLoading('正在删除位置...')

          // 构建删除参数，使用uuid
          const deleteParams = {
            mapId: this.currentMapId,
            uuid: this.selectedObject.uuid
          }

          // 先调用删除接口
          const res = await deletePosition(deleteParams)

          if (res.errorCode === 0) {
            // 接口删除成功，执行本地删除操作
            this.performLocalDelete()
            hideLoading()
            message.success('位置删除成功')
          } else {
            // 接口删除失败，显示错误信息
            hideLoading()
            requestSuccess(res)
          }
        } catch (error) {
          hideLoading()
          requestFailed(error)
        }
      }
    },

    // 执行本地删除操作（从3D场景和树形结构中删除）
    performLocalDelete() {
      if (!this.selectedPositionData) return

      console.log('开始执行本地删除操作:', this.selectedPositionData.name)

      // 从3D场景中删除对应对象
      // 优先通过uuid匹配，避免重名问题
      let sceneObjectIndex = -1
      if (this.selectedPositionData.uuid) {
        sceneObjectIndex = this.sceneObjects.findIndex(obj => obj.uuid === this.selectedPositionData.uuid)
      }

      // 如果没有uuid或未找到，则通过名称匹配（选择当前选中的对象）
      if (sceneObjectIndex === -1 && this.selectedObject) {
        sceneObjectIndex = this.sceneObjects.findIndex(obj => obj === this.selectedObject)
      }

      // 最后通过名称匹配
      if (sceneObjectIndex === -1) {
        sceneObjectIndex = this.sceneObjects.findIndex(obj => obj.name === this.selectedPositionData.name)
      }

      if (sceneObjectIndex !== -1) {
        const object = this.sceneObjects[sceneObjectIndex]
        console.log('从3D场景删除对象:', object.name)
        this.scene.remove(object.mesh)
        this.sceneObjects.splice(sceneObjectIndex, 1)
      } else {
        console.warn('未找到要删除的3D对象:', this.selectedPositionData.name)
      }

      // 从树形数据中删除，使用更精确的匹配方式
      const removed = this.treeDataManager.removePositionByData(this.currentMapId, this.selectedPositionData)
      if (removed) {
        this.treeData = this.treeDataManager.getTreeData()
        console.log('树形结构删除成功')
      } else {
        console.warn('树形结构删除失败')
      }

      // 清空选择
      this.selectedPositionData = null
      this.selectedObject = null
      this.selectedKeys = []
      this.treeDataManager.setSelectedKeys([])
    },

    // 异步加载树节点数据（现在只是占位，实际加载在点击时进行）
    async onLoadData(treeNode) {
      console.log('onLoadData', treeNode)
      // 如果已经有子节点，直接返回
      if (treeNode.dataRef.children) {
        return
      }

      // 初始化为空数组，等待用户点击时加载
      treeNode.dataRef.children = []
      this.treeData = [...this.treeData]
    },

    // 切换PCD渲染状态
    async togglePCD() {
      if (this.isPCDRendered) {
        this.clearPCD()
      } else {
        await this.downloadAndRenderPCD()
      }
    },

    // 下载并渲染PCD文件
    async downloadAndRenderPCD() {
      if (!this.currentMapData || !this.currentMapData.pcdName) {
        message.warning('当前地图没有PCD文件')
        return
      }

      try {
        showLoading('正在渲染PCD地图...')

        // 调用下载接口获取PCD文件
        const pcdResponse = await downloadMapFile(this.currentMapData.id, this.currentMapData.pcdName)
        console.log('PCD文件下载成功:', pcdResponse)

        if (pcdResponse) {
          // 接口返回的直接就是blob数据，创建URL
          const pcdUrl = URL.createObjectURL(pcdResponse)

          // 使用PCDRenderer渲染PCD文件，PCD文件已经是米单位，不需要小缩放
          const result = await this.pcdRenderer.renderPCD(pcdUrl, 1.0, 0.05)

          // 调整相机位置以适应PCD点云
          this.adjustCameraForPCD(result.bounds)

          // 限制现有位置元素在PCD范围内
          this.clampExistingPositionsToPCD()

          // 清理临时URL
          URL.revokeObjectURL(pcdUrl)

          // 更新状态
          this.isPCDRendered = true

          hideLoading()
        } else {
          hideLoading()
          message.error('PCD文件加载失败')
        }
      } catch (error) {
        hideLoading()
        message.error('PCD文件处理失败: ' + error.message)
        console.error('PCD文件处理错误:', error)
      }
    },

    // 清除PCD渲染
    clearPCD() {
      if (this.pcdRenderer) {
        this.pcdRenderer.dispose()

        // 重置拖拽管理器边界
        this.resetDragManagerBounds()

        // 重置相机位置
        this.resetCameraPosition()

        // 更新状态
        this.isPCDRendered = false

        message.success('已清除PCD点云')
      }
    },

    // 调整相机位置以适应PCD点云
    adjustCameraForPCD(bounds) {
      if (!bounds || !this.camera || !this.controls) return

      // 计算合适的相机距离
      const maxDimension = Math.max(bounds.width, bounds.height, bounds.depth)
      const distance = maxDimension * 2 // 相机距离为点云最大尺寸的2倍

      // 设置相机位置（斜上方视角）
      this.camera.position.set(
        bounds.centerX + distance * 0.5,
        bounds.centerY + distance * 0.8,
        bounds.centerZ + distance * 0.5
      )
      this.camera.lookAt(bounds.centerX, bounds.centerY, bounds.centerZ)

      // 更新控制器目标点
      this.controls.target.set(bounds.centerX, bounds.centerY, bounds.centerZ)
      this.controls.update()

      console.log(`相机已调整到PCD点云视角，距离: ${distance}`)
    },

    // 限制现有位置元素在PCD范围内
    clampExistingPositionsToPCD() {
      if (!this.pcdRenderer || !this.pcdRenderer.hasPCD()) return

      let clampedCount = 0
      this.sceneObjects.forEach(obj => {
        if (obj.type === 'person') {
          const originalPos = { x: obj.position.x, y: obj.position.y, z: obj.position.z }
          const clampedPoint = this.pcdRenderer.clampPointToPCDBounds(originalPos.x, originalPos.z)
          const clampedPos = {
            x: clampedPoint.x,
            y: originalPos.y, // Y坐标保持不变
            z: clampedPoint.z
          }

          // 如果位置发生了变化，更新对象位置
          if (clampedPos.x !== originalPos.x || clampedPos.z !== originalPos.z) {
            obj.position.copy(clampedPos)
            if (obj.mesh) {
              obj.mesh.position.copy(clampedPos)
            }
            clampedCount++

            // 同步更新位置数据
            this.updatePositionDataFromObject(obj)
          }
        }
      })

      if (clampedCount > 0) {
        console.log(`已将 ${clampedCount} 个位置元素限制在PCD范围内`)
        message.info(`已调整 ${clampedCount} 个位置元素到点云范围内`)
      }
    },

    // 更新PCD渲染器的边界
    updateDragManagerBoundsForPCD(bounds) {
      if (this.dragManager && bounds) {
        this.dragManager.setBounds({
          minX: bounds.minX,
          maxX: bounds.maxX,
          minZ: bounds.minZ,
          maxZ: bounds.maxZ,
          minY: 0 // Y轴最小值保持为0（地面）
        })
        console.log('拖拽边界已更新为PCD范围:', bounds)
      }
    },

    // 清除PGM渲染
    clearPGM() {
      if (this.pgmRenderer) {
        this.pgmRenderer.dispose()

        // 恢复默认地板和网格
        SceneSetup.showDefaultFloor(this.scene)

        // 重置拖拽管理器边界
        this.resetDragManagerBounds()

        // 重置相机位置
        this.resetCameraPosition()

        message.success('已清除PGM地图，恢复默认地板和拖拽范围')
        console.log('PGM地图已清除')
      }
    },

    // 重置相机位置
    resetCameraPosition() {
      if (this.camera && this.controls) {
        this.camera.position.set(10, 10, 10)
        this.camera.lookAt(0, 0, 0)
        this.controls.target.set(0, 0, 0)
        this.controls.update()
      }
    },

    // 更新拖拽管理器的边界
    updateDragManagerBounds(bounds) {
      if (this.dragManager && bounds) {
        this.dragManager.setBounds({
          minX: bounds.minX,
          maxX: bounds.maxX,
          minZ: bounds.minZ,
          maxZ: bounds.maxZ,
          minY: 0 // Y轴最小值保持为0（地面）
        })
        console.log('拖拽边界已更新为PGM范围:', bounds)
      }
    },

    // 重置拖拽管理器边界为默认值
    resetDragManagerBounds() {
      if (this.dragManager) {
        this.dragManager.resetBounds()
        console.log('拖拽边界已重置为默认值')
      }
    },

    // 调整相机位置以适应PGM地图
    adjustCameraForPGM(bounds) {
      if (!bounds || !this.camera || !this.controls) return

      // 计算合适的相机距离
      const maxDimension = Math.max(bounds.width, bounds.height)
      const distance = maxDimension * 1.5 // 相机距离为地图最大尺寸的1.5倍

      // 设置相机位置（俯视角度）
      this.camera.position.set(0, distance, distance * 0.5)
      this.camera.lookAt(0, 0, 0)

      // 更新控制器目标点
      this.controls.target.set(0, 0, 0)
      this.controls.update()

      console.log(`相机已调整到PGM地图视角，距离: ${distance}`)
    },

    // 限制现有位置元素在PGM范围内
    clampExistingPositionsToPGM() {
      if (!this.pgmRenderer || !this.pgmRenderer.hasPGM()) return

      let clampedCount = 0
      this.sceneObjects.forEach(obj => {
        if (obj.type === 'person') {
          const originalPos = { x: obj.position.x, y: obj.position.y, z: obj.position.z }
          const clampedPos = this.positionManager.validateAndClampPosition(originalPos, this.pgmRenderer)

          // 如果位置发生了变化，更新对象位置
          if (clampedPos.x !== originalPos.x || clampedPos.z !== originalPos.z) {
            obj.position.copy(clampedPos)
            if (obj.mesh) {
              obj.mesh.position.copy(clampedPos)
            }
            clampedCount++

            // 同步更新位置数据
            this.updatePositionDataFromObject(obj)
          }
        }
      })

      if (clampedCount > 0) {
        console.log(`已将 ${clampedCount} 个位置元素限制在PGM范围内`)
        message.info(`已调整 ${clampedCount} 个位置元素到地图范围内`)
      }
    },

    // 从3D对象更新位置数据
    updatePositionDataFromObject(object) {
      if (!object || !this.currentMapId) return

      // 查找对应的树节点
      const treeNode = this.treeDataManager.findTreeNodeByObject(object, this.currentMapId)
      if (treeNode && treeNode.objectData) {
        // 更新树节点中的位置数据
        treeNode.objectData.xcoordinate = object.position.x.toString()
        treeNode.objectData.ycoordinate = object.position.y.toString()
        treeNode.objectData.zcoordinate = object.position.z.toString()

        // 如果当前选中的是这个对象，也更新选中的位置数据
        if (this.selectedObject === object && this.selectedPositionData) {
          this.selectedPositionData.xcoordinate = object.position.x.toString()
          this.selectedPositionData.ycoordinate = object.position.y.toString()
          this.selectedPositionData.zcoordinate = object.position.z.toString()
        }
      }
    },

    // 根据mapId查询位置点数据（现在通过getMapInfo获取）
    async fetchPositionsByMapId(mapId) {
      try {
        const res = await getMapInfo(mapId)
        if (res.errorCode === 0 && res.data && res.data.positionInformations) {
          return res.data.positionInformations
        }
        return []
      } catch (error) {
        console.error('获取位置数据失败:', error)
        return []
      }
    },

    // 将位置点数据转换为3D场景对象格式
    convertPositionDataToSceneObjects(positionData) {
      return positionData.map(pos => ({
        name: pos.name,
        type: 'person', // 默认类型为person
        position: {
          x: parseFloat(pos.xcoordinate) || 0,
          y: parseFloat(pos.ycoordinate) || 0,
          z: parseFloat(pos.zcoordinate) || 0
        },
        rotation: (parseFloat(pos.yaw) || 0) * Math.PI / 180, // 将角度转换为弧度
        actionNo: pos.actionNo || '', // 保留动作编号
        contentDetail: pos.contentDetail || '', // 保留内容详情
        uuid: pos.uuid, // 传递uuid
        isSaved: true // 从服务器加载的数据标记为已保存
      }))
    },

    // 树节点展开事件
    async onTreeExpand(expandedKeys, { expanded, node }) {
      // 同步展开状态到 treeDataManager
      this.treeDataManager.setExpandedKeys(expandedKeys)

      if (expanded && !node.isLeaf) {
        // 展开时只需要确保3D场景加载了对应的地图，不需要重新构建树形结构
        const mapId = parseInt(node.key) || node.key
        if (this.currentMapId !== mapId) {
          await this.loadMap(mapId)
        }
      }
    },

    // 根据key查找树节点
    findTreeNodeByKey(key) {
      for (const mapNode of this.treeData) {
        if (mapNode.key === key) {
          return mapNode
        }
        if (mapNode.children) {
          for (const childNode of mapNode.children) {
            if (childNode.key === key) {
              return childNode
            }
          }
        }
      }
      return null
    },

    // 树节点选择事件
    async onTreeSelect(selectedKeys, info) {
      console.log('树节点选择事件:', selectedKeys, info)

      // 同步选中状态到组件和 treeDataManager
      this.selectedKeys = selectedKeys
      this.treeDataManager.setSelectedKeys(selectedKeys)

      if (selectedKeys.length === 0) {
        // 清空选择，同步取消3D场景中的选中状态
        this.selectObject(null) // 这会同时清空selectedObject和selectedPositionData
        this.currentSelectedMapId = null
        return
      }

      const selectedKey = selectedKeys[0]
      console.log('树形选择事件 - selectedKey:', selectedKey)

      // 🔥 关键修复：直接通过selectedKey查找对应的树节点，而不是使用info.selectedNodes[0]
      const selectedNode = this.findTreeNodeByKey(selectedKey)

      if (!selectedNode) {
        console.error(`未找到key为 ${selectedKey} 的树节点`)
        return
      }

      if (selectedNode.isLeaf) {
        // 选中的是位置节点
        this.selectedPositionData = { ...selectedNode.objectData } // 复制数据以便编辑

        // 设置当前选中的地图ID
        const { mapId } = selectedNode
        this.currentSelectedMapId = mapId

        // 确保地图已加载，如果没有加载则先加载
        if (this.currentMapId !== mapId) {
          await this.loadMap(mapId)
        }

        // 在3D场景中选中对应的对象
        // 优先通过uuid查找，其次通过树节点key精确匹配
        if (selectedNode.objectData && selectedNode.objectData.uuid) {
          console.log(`通过uuid选择对象: ${selectedNode.objectData.uuid}`)
          this.selectObjectByUUID(selectedNode.objectData.uuid)
        } else {
          console.log('objectData没有uuid，使用树节点key匹配')
          // 使用树节点的key来精确匹配对象
          this.selectObjectByTreeNodeKey(selectedNode.key, selectedNode.objectData)
        }

        // 强制更新树形组件的选中状态
        this.$nextTick(() => {
          this.selectedKeys = [selectedKey]
        })
      } else {
        // 选中的是地图节点，取消3D场景中的选中状态
        this.selectObject(null) // 取消3D对象选中
        this.selectedPositionData = null // 清空位置数据选择
        this.currentSelectedMapId = selectedKey // 设置当前选中的地图ID

        // 如果当前显示的不是这个地图，才需要重新加载
        const mapId = parseInt(selectedKey) || selectedKey
        if (this.currentMapId !== mapId) {
          await this.loadMap(mapId)
        }

        // 自动展开选中的地图节点
        if (!this.expandedKeys.includes(selectedKey)) {
          this.expandedKeys.push(selectedKey)
        }
      }
    },

    // 根据uuid在3D场景中选中对象
    selectObjectByUUID(uuid) {
      if (!uuid) return

      const targetObject = this.sceneObjects.find(obj => obj.uuid === uuid)

      if (targetObject) {
        this.selectObject(targetObject)
        console.log(`已通过uuid选中对象: ${targetObject.uuid}`)
      } else {
        console.warn(`未找到uuid为 ${uuid} 的3D对象`)
      }
    },

    // 根据树节点key精确选中对象（处理重名情况）
    selectObjectByTreeNodeKey(nodeKey, objectData) {
      if (!nodeKey || !objectData) return

      // 首先尝试通过uuid匹配
      if (objectData.uuid) {
        const targetObject = this.sceneObjects.find(obj => obj.uuid === objectData.uuid)
        if (targetObject) {
          this.selectObject(targetObject)
          console.log(`已通过uuid选中对象: ${targetObject.name}`)
          return
        }
      }

      // 最后通过名称匹配，但要考虑创建时间（选择最新的同名对象）
      const sameNameObjects = this.sceneObjects.filter(obj => obj.name === objectData.name)
      if (sameNameObjects.length > 0) {
        // 如果有多个同名对象，选择最新创建的（数组中最后一个）
        const targetObject = sameNameObjects[sameNameObjects.length - 1]
        this.selectObject(targetObject)
        console.log(`已通过名称选中对象（最新）: ${targetObject.name}`)
      } else {
        console.warn(`未找到匹配的3D对象，nodeKey: ${nodeKey}, name: ${objectData.name}`)
      }
    },

    // 加载地图并将位置点添加为子级（现在通过getMapInfo接口）
    async loadMapWithPositions(mapId) {
      try {
        // 调用 getMapInfo 接口获取地图详细信息
        const res = await getMapInfo(mapId)
        if (res.errorCode === 0) {
          // 处理地图信息和位置信息
          await this.processMapInfo(res.data)
          console.log(`已加载地图: ${mapId} 及其位置点数据`)
        } else {
          requestSuccess(res)
        }
      } catch (error) {
        console.error('加载地图和位置点数据失败:', error)
        requestFailed(error)
      }
    },

    // 加载地图数据
    async loadMap(mapId) {
      const mapData = this.mapList.find(map => map.id === mapId)
      if (!mapData) return

      this.currentMapId = mapId

      // 清空当前场景
      this.clearSceneWithoutConfirm()

      try {
        // 获取该地图的位置点数据
        const positionData = await this.fetchPositionsByMapId(mapId)

        if (positionData && positionData.length > 0) {
          // 将位置点数据转换为3D场景可用的格式
          const sceneObjects = this.convertPositionDataToSceneObjects(positionData)
          // 导入位置点数据到场景中
          this.importObjects(sceneObjects)
        }

        console.log(`已加载地图: ${mapData.name}`)
      } catch (error) {
        console.error('加载地图数据失败:', error)
      }
    },



    initThreeJS() {
      const container = this.$refs.threeContainer

      // 使用SceneSetup初始化场景
      const sceneConfig = SceneSetup.initThreeJS(container)
      this.scene = sceneConfig.scene
      this.camera = sceneConfig.camera
      this.renderer = sceneConfig.renderer
      this.controls = sceneConfig.controls

      // 初始化PGM渲染器
      this.pgmRenderer = new PGMRenderer(this.scene)

      // 初始化PCD渲染器
      this.pcdRenderer = new PCDRenderer(this.scene)

      // 添加光源
      SceneSetup.setupLighting(this.scene)

      // 添加地面
      SceneSetup.createFloor(this.scene)

      // 初始化拖拽管理器
      this.dragManager = markRaw(new DragManager(this.scene, this.camera, this.renderer.domElement))
      this.dragManager.setControls(this.controls)
      this.dragManager.setObjectPositionUpdateCallback(this.onDraggedObjectPositionUpdate)

      // 设置事件监听
      SceneSetup.setupWindowResize(this.camera, this.renderer, container)
      SceneSetup.setupClickHandler(this.renderer, this.camera, () => this.sceneObjects, this.selectObject.bind(this))
      SceneSetup.setupWheelHandler(this.renderer, this.onCanvasWheel)
    },



    // 设置拖拽元素类型
    startDrag(type, event) {
      event.dataTransfer.setData('text/plain', type)
    },

    // 拖放事件
    setupDragAndDrop() {
      const container = this.$refs.threeContainer
      SceneSetup.setupDragAndDrop(container, this.createObjectAtPosition.bind(this))
    },

    // 创建对象并放置在指定位置
    createObjectAtPosition(type, event) {
      // 计算地面交点
      let intersectPoint = this.positionManager.calculateGroundIntersection(
        event,
        this.$refs.threeContainer,
        this.camera
      )

      // 优先使用PGM边界限制，其次使用PCD边界限制
      if (this.pgmRenderer && this.pgmRenderer.hasPGM()) {
        const clampedPoint = this.pgmRenderer.clampPointToPGMBounds(intersectPoint.x, intersectPoint.z)
        intersectPoint.x = clampedPoint.x
        intersectPoint.z = clampedPoint.z
      } else if (this.pcdRenderer && this.pcdRenderer.hasPCD()) {
        const clampedPoint = this.pcdRenderer.clampPointToPCDBounds(intersectPoint.x, intersectPoint.z)
        intersectPoint.x = clampedPoint.x
        intersectPoint.z = clampedPoint.z
      }

      // 创建对象并自动添加到对应地图的子级
      this.createObjectAndAddToTree(type, intersectPoint)
    },

    // 创建对象并添加到树形结构
    createObjectAndAddToTree(type, position) {
      // 如果没有选中的地图，使用当前加载的地图
      const targetMapId = this.currentSelectedMapId || this.currentMapId

      if (!targetMapId) {
        message.warning('请先打开一个地图')
        return
      }

      // 获取当前地图中已有的所有位置信息
      const existingPositions = this.getCurrentMapPositions(targetMapId)

      // 使用PositionManager创建位置数据，传入已有位置信息以避免重名
      const { positionData, objectData } = this.positionManager.createPositionData(targetMapId, position, existingPositions)

      // 创建3D对象
      this.createObject(type, position, objectData)

      // 添加到树形结构
      const newNodeKey = this.treeDataManager.addPositionToTree(targetMapId, positionData)

      // 同步树形数据
      this.treeData = this.treeDataManager.getTreeData()
      this.expandedKeys = this.treeDataManager.getExpandedKeys()

      // 自动选中新添加的位置元素
      this.selectNewlyAddedPosition(newNodeKey, positionData)

      console.log(`已创建位置点并添加到地图 ${targetMapId}: ${positionData.name}`)
    },

    // 获取当前地图中已有的所有位置信息
    getCurrentMapPositions(mapId) {
      const positions = []

      // 从树形数据中获取位置信息
      const mapNode = this.treeData.find(node => node.key === mapId.toString() || node.key === mapId)
      if (mapNode && mapNode.children) {
        mapNode.children.forEach(child => {
          if (child.objectData) {
            positions.push({
              name: child.objectData.name,
              uuid: child.objectData.uuid
            })
          }
        })
      }

      return positions
    },

    // 根据已有位置更新位置计数器
    updatePositionCounterFromExistingPositions(mapId, existingPositions) {
      if (!existingPositions || existingPositions.length === 0) {
        return
      }

      // 找出所有符合"位置X"格式的名称，并提取最大的数字
      let maxNumber = 0
      existingPositions.forEach(position => {
        const match = position.name.match(/^位置(\d+)$/)
        if (match) {
          const number = parseInt(match[1])
          if (number > maxNumber) {
            maxNumber = number
          }
        }
      })

      // 设置位置计数器为最大数字+1，确保新建位置不会重名
      if (maxNumber > 0) {
        this.positionManager.setPositionCounter(mapId, maxNumber + 1)
        console.log(`已更新地图 ${mapId} 的位置计数器为: ${maxNumber + 1}`)
      }
    },

    // 自动选中新添加的位置元素
    selectNewlyAddedPosition(nodeKey, positionData) {
      if (!nodeKey) return

      // 使用nextTick确保DOM更新完成后再选中
      this.$nextTick(() => {
        // 设置选中的节点
        this.selectedKeys = [nodeKey]
        this.treeDataManager.setSelectedKeys(this.selectedKeys)

        // 设置位置数据到属性面板
        this.selectedPositionData = { ...positionData }

        // 设置当前选中的地图ID
        const [mapId] = nodeKey.split('_')
        this.currentSelectedMapId = mapId

        // 在3D场景中选中对应的对象
        // 对于新建的位置元素，通过uuid查找
        this.selectObjectByUUID(positionData.uuid)

        console.log(`已自动选中新添加的位置: ${positionData.name}`)
      })
    },

    createObject(type, position, businessData = null) {
      // 验证position参数
      if (!position || typeof position.x !== 'number' || typeof position.y !== 'number' || typeof position.z !== 'number') {
        console.error('createObject: Invalid position parameter:', position)
        position = new THREE.Vector3(0, 0, 0)
      }

      let object
      const name = businessData?.name || `位置${Date.now()}`

      try {
        switch (type) {
          case 'person': {
            // 计算适合当前地图分辨率的缩放比例
            const scale = this.calculatePersonScale()
            object = makeThreeObjectNonReactive(new Person(name, name, position, scale))
            // 如果有业务数据，保存到对象中
            if (businessData) {
              // 设置uuid到Person对象中，用于userData
              object.uuid = businessData.uuid || null
              object.actionNo = businessData.actionNo || ''
              object.contentDetail = businessData.contentDetail || ''
              // 保存状态标记
              object.isSaved = businessData.isSaved || false
              if (businessData.rotation !== undefined) {
                object.setRotation(businessData.rotation)
              }
            }
            break
          }
          default:
            console.warn('createObject: Unknown object type:', type)
            return
        }

        if (!object || !object.mesh) {
          console.error('createObject: Failed to create object or mesh')
          return
        }

        this.scene.add(object.mesh)
        this.sceneObjects.push(object)

        // 性能优化：更新拖拽管理器的可拖拽对象缓存
        if (this.dragManager) {
          this.dragManager.updateDraggableObjectsCache(this.sceneObjects)
        }

        // 正确选中新创建的对象（这会取消之前对象的选中状态）
        this.selectObject(object)
      } catch (error) {
        console.error(`Error creating ${type} object:`, error)
      }
    },

    selectObject(object) {
      // 取消之前选中对象的高亮
      if (this.selectedObject && this.selectedObject.setSelected) {
        this.selectedObject.setSelected(false)
      }

      this.selectedObject = object

      // 高亮当前选中对象
      if (object && object.setSelected) {
        object.setSelected(true)
      }

      // 设置位置数据到属性面板
      this.setPositionDataFromObject(object)

      // 同步选中树形结构中对应的节点
      if (object) {
        this.syncTreeSelection(object)
      } else {
        // 取消选中时，清空树形结构的选中状态
        this.selectedKeys = []
        this.treeDataManager.setSelectedKeys([])
      }

      // 强制更新Vue界面
      this.$forceUpdate()
    },

    // 同步选中树形结构中对应的节点
    syncTreeSelection(object) {
      const treeNode = this.treeDataManager.findTreeNodeByObject(object, this.currentMapId)

      if (treeNode) {
        // 选中对应的树节点
        this.selectedKeys = [treeNode.key]
        this.treeDataManager.setSelectedKeys(this.selectedKeys)
        console.log(`已同步选中树节点: ${treeNode.title}`)
      }
    },

    // 从3D对象设置位置数据（统一属性面板）
    setPositionDataFromObject(object) {
      this.selectedPositionData = this.positionManager.setPositionDataFromObject(object)
    },

    // 更新位置点属性
    updatePositionProperty(property, value) {
      if (this.selectedPositionData) {
        // 对坐标属性进行边界限制
        if (['xcoordinate', 'ycoordinate', 'zcoordinate'].includes(property)) {
          const numValue = parseFloat(value) || 0
          let clampedValue = numValue

          // 根据属性类型应用相应的边界限制
          if (property === 'xcoordinate') {
            clampedValue = Math.max(this.coordinateBounds.minX, Math.min(this.coordinateBounds.maxX, numValue))
          } else if (property === 'ycoordinate') {
            clampedValue = Math.max(this.coordinateBounds.minY, Math.min(this.coordinateBounds.maxY, numValue))
          } else if (property === 'zcoordinate') {
            clampedValue = Math.max(this.coordinateBounds.minZ, Math.min(this.coordinateBounds.maxZ, numValue))
          }

          // 如果值被限制了，显示提示并更新输入框
          if (clampedValue !== numValue) {
            message.info(`坐标值已限制在有效范围内: ${clampedValue}`)
            // 更新输入框显示的值
            this.selectedPositionData[property] = clampedValue.toString()
          }

          value = clampedValue
        }

        // 使用PositionManager更新属性
        this.positionManager.updatePositionProperty(this.selectedPositionData, property, value)

        // 同步更新树形数据中的objectData
        const updated = this.treeDataManager.updateTreeNodeObjectData(
          property,
          value,
          this.currentMapId,
          this.selectedKeys
        )

        if (updated) {
          this.treeData = this.treeDataManager.getTreeData()
        }

        // 更新3D场景中的对象
        this.positionManager.updateSceneObjectFromPositionData(
          this.selectedPositionData,
          this.selectedObject,
          property,
          value
        )
      }
    },



    // 鼠标滚轮事件处理
    onCanvasWheel(event) {
      // 只有在选中对象且按住Shift键时才处理滚轮事件
      if (!this.selectedObject || !this.selectedPositionData || !this.isShiftPressed) {
        return // 让OrbitControls处理缩放
      }

      // 完全阻止事件传播，防止OrbitControls处理
      event.preventDefault()
      event.stopPropagation()
      event.stopImmediatePropagation()

      // 计算旋转增量（每次滚动15度）
      const rotationDelta = event.deltaY > 0 ? 15 : -15

      // 使用PositionManager处理旋转
      const newYaw = this.positionManager.handleRotation(this.selectedPositionData, rotationDelta)

      if (newYaw !== null) {
        // 更新位置数据
        this.selectedPositionData.yaw = newYaw.toString()
        this.updatePositionProperty('yaw', newYaw.toString())

        // 显示临时提示
        this.showRotationHint(newYaw, 'Shift+滚轮调整')
      }

      return false // 确保事件不会继续传播
    },

    // 键盘状态监听（用于跟踪Shift键状态）
    onKeyStateChange(event) {
      this.isShiftPressed = event.shiftKey

      // 当Shift键状态改变时，动态控制OrbitControls的缩放功能
      if (this.controls && this.selectedObject && this.selectedPositionData) {
        if (this.isShiftPressed) {
          // 按下Shift时，如果有选中对象，禁用缩放
          this.controls.enableZoom = false
        } else {
          // 释放Shift时，重新启用缩放
          this.controls.enableZoom = true
        }
      }
    },

    // 键盘事件处理
    onKeyDown(event) {
      // 只有在选中对象时才处理键盘事件
      if (!this.selectedObject || !this.selectedPositionData) {
        return
      }

      // 检查是否在输入框中，如果是则不处理
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return
      }

      let rotationDelta = 0

      switch (event.key) {
        case 'q':
        case 'Q':
          rotationDelta = -15 // 逆时针15度
          break
        case 'e':
        case 'E':
          rotationDelta = 15 // 顺时针15度
          break
        case 'ArrowLeft':
          if (event.ctrlKey) {
            rotationDelta = -15
          }
          break
        case 'ArrowRight':
          if (event.ctrlKey) {
            rotationDelta = 15
          }
          break
        default:
          return // 不处理其他按键
      }

      if (rotationDelta !== 0) {
        event.preventDefault()

        // 使用PositionManager处理旋转
        const newYaw = this.positionManager.handleRotation(this.selectedPositionData, rotationDelta)

        if (newYaw !== null) {
          // 更新位置数据
          this.selectedPositionData.yaw = newYaw.toString()
          this.updatePositionProperty('yaw', newYaw.toString())

          // 显示提示
          this.showRotationHint(newYaw, `按键: ${event.key.toUpperCase()}`)
        }
      }
    },

    // 显示旋转提示（增强版）
    showRotationHint(yaw, method = 'Shift+滚轮调整') {
      // 创建或更新提示元素
      let hint = document.getElementById('rotation-hint')
      if (!hint) {
        hint = document.createElement('div')
        hint.id = 'rotation-hint'
        hint.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 10px 15px;
          border-radius: 5px;
          font-size: 14px;
          z-index: 1000;
          pointer-events: none;
          transition: opacity 0.3s;
        `
        document.body.appendChild(hint)
      }

      hint.innerHTML = `
        <div>朝向: ${Math.round(yaw)}°</div>
        <div style="font-size: 12px; opacity: 0.8; margin-top: 2px;">${method}</div>
      `
      hint.style.opacity = '1'

      // 2秒后淡出
      clearTimeout(this.hintTimeout)
      this.hintTimeout = setTimeout(() => {
        if (hint) {
          hint.style.opacity = '0'
          setTimeout(() => {
            if (hint && hint.parentNode) {
              hint.parentNode.removeChild(hint)
            }
          }, 300)
        }
      }, 2000)
    },



    // 持续更新渲染 - 性能优化版本
    animate() {
      requestAnimationFrame(this.animate)

      // 更新性能监控
      performanceMonitor.update(this.renderer)

      // 自动渲染优化
      renderOptimizer.autoOptimize(
        performanceMonitor.getStats(),
        this.pgmRenderer,
        this.pcdRenderer
      )

      // 更新相机控制器
      this.controls.update()

      // 更新PCD LOD（如果存在）
      if (this.pcdRenderer && this.pcdRenderer.hasPCD()) {
        this.pcdRenderer.updateLOD(this.camera)
      }

      // 更新所有场景对象
      this.sceneObjects.forEach(obj => {
        if (obj.update) {
          obj.update()
        }
      })

      // 渲染场景
      this.renderer.render(this.scene, this.camera)
    },

    // 初始化性能监控
    initPerformanceMonitoring() {
      // 启动性能监控
      performanceMonitor.start()

      // 设置性能阈值
      performanceMonitor.setThresholds({
        lowFPS: 25,
        highMemory: 300 * 1024 * 1024, // 300MB
        maxPoints: 150000
      })

      // 添加性能回调
      performanceMonitor.addCallback((stats) => {
        this.performanceStats = {
          fps: stats.fps,
          memoryMB: stats.memoryMB,
          points: stats.points
        }

        // 自动优化建议
        if (stats.fps < 20 && this.pcdRenderer && this.pcdRenderer.hasPCD()) {
          console.warn('FPS过低，建议减少点云密度')
          // 可以在这里自动切换到更低的LOD
        }
      })

      console.log('性能监控已初始化')
    },

    // 切换性能面板显示
    togglePerformancePanel() {
      this.showPerformancePanel = !this.showPerformancePanel
    },

    // 优化渲染性能
    optimizePerformance(advice = null) {
      // 如果没有传入advice，则获取当前的性能建议
      if (!advice) {
        const stats = performanceMonitor.getStats()
        advice = renderOptimizer.getOptimizationAdvice(stats)
        console.log('性能统计:', stats)
        console.log('优化建议:', advice)

        // 应用自动优化
        renderOptimizer.autoOptimize(stats, this.pgmRenderer, this.pcdRenderer)
      }

      let optimized = false

      // 应用优化建议
      if (advice.reducePCDDensity && this.pcdRenderer && this.pcdRenderer.hasPCD()) {
        this.pcdRenderer.setLOD(Math.min(this.pcdRenderer.currentLOD + 1, 3))
        optimized = true
        console.log('已降低PCD密度')
      }

      if (advice.reducePGMQuality && this.pgmRenderer && this.pgmRenderer.hasPGM()) {
        // 可以在这里添加PGM质量降低的逻辑
        console.log('建议降低PGM质量')
      }

      if (advice.enableFrustumCulling && this.renderer) {
        // 启用视锥体剔除
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5))
        optimized = true
        console.log('已启用视锥体剔除优化')
      }

      if (advice.reduceRenderDistance && this.renderer && this.camera) {
        // 减少渲染距离
        this.camera.far = Math.max(this.camera.far * 0.8, 100)
        this.camera.updateProjectionMatrix()
        optimized = true
        console.log('已减少渲染距离')
      }

      // 根据建议执行特定优化
      if (Array.isArray(advice)) {
        advice.forEach(item => {
          switch (item.action) {
            case 'reduce_quality':
              renderOptimizer.applyPreset('performance', this.pgmRenderer, this.pcdRenderer)
              message.info('已切换到性能优先模式')
              break
            case 'clear_cache':
              if (this.pgmRenderer) {
                this.pgmRenderer.clearCache()
              }
              if (this.pcdRenderer) {
                this.pcdRenderer.clearCache()
              }
              message.info('已清理缓存以释放内存')
              break
            case 'enable_lod':
              if (this.pcdRenderer && this.pcdRenderer.hasPCD()) {
                // 强制切换到较低的LOD
                const lodLevels = this.pcdRenderer.lodLevels.length
                if (lodLevels > 1) {
                  this.pcdRenderer.switchToLOD(Math.min(2, lodLevels - 1))
                  message.info('已启用LOD优化')
                }
              }
              break
          }
        })
      }

      if (optimized || (Array.isArray(advice) && advice.length > 0)) {
        message.success('性能优化已应用')
      } else {
        message.success('当前性能良好，无需优化')
      }
    },



    // 获取渲染优化状态
    getRenderOptimizerStatus() {
      return renderOptimizer.getCurrentSettings()
    },

    // 清理PCD渲染
    clearPCDRendering() {
      if (this.pcdRenderer) {
        console.log('清理PCD渲染...')
        console.log('PCD渲染状态:', this.isPCDRendered)
        console.log('PCD网格存在:', this.pcdRenderer.hasPCD())

        this.pcdRenderer.dispose()
        this.isPCDRendered = false
        console.log('PCD渲染已清理')
      }
    },

    // 清理PGM渲染
    clearPGMRendering() {
      if (this.pgmRenderer) {
        console.log('清理PGM渲染...')
        this.pgmRenderer.dispose()
        console.log('PGM渲染已清理')
      }
    },

    // 清理所有地图渲染
    clearAllMapRendering() {
      console.log('开始清理所有地图渲染...')
      this.clearPCDRendering()
      this.clearPGMRendering()
      console.log('所有地图渲染清理完成')
    },



    // 拖拽对象位置更新回调
    onDraggedObjectPositionUpdate(objectType, objectUuid, newPosition) {
      // 找到对应的对象
      const object = this.sceneObjects.find(obj => obj.uuid === objectUuid && obj.type === objectType)
      if (!object) {
        return
      }

      // 限制位置在边界范围内（优先PGM，其次PCD）
      let clampedPosition = newPosition
      if (this.pgmRenderer && this.pgmRenderer.hasPGM()) {
        clampedPosition = this.positionManager.validateAndClampPosition(newPosition, this.pgmRenderer)

        // 如果位置被限制了，显示提示
        if (clampedPosition.x !== newPosition.x || clampedPosition.z !== newPosition.z) {
          console.log('拖拽位置被限制在PGM范围内')
        }
      } else if (this.pcdRenderer && this.pcdRenderer.hasPCD()) {
        const clampedPoint = this.pcdRenderer.clampPointToPCDBounds(newPosition.x, newPosition.z)
        clampedPosition = {
          x: clampedPoint.x,
          y: newPosition.y,
          z: clampedPoint.z
        }

        // 如果位置被限制了，显示提示
        if (clampedPosition.x !== newPosition.x || clampedPosition.z !== newPosition.z) {
          console.log('拖拽位置被限制在PCD范围内')
        }
      }

      // 更新对象的内部位置
      object.position.copy(clampedPosition)

      // 根据对象类型进行特殊处理
      switch (objectType) {
        case 'person':
          // 人物对象：确保mesh位置同步
          if (object.mesh) {
            object.mesh.position.copy(clampedPosition)
          }
          break
      }

      // 同步更新位置数据到树形结构
      this.updatePositionDataFromObject(object)

      // 如果当前选中的是这个对象，触发界面更新
      if (this.selectedObject && this.selectedObject.uuid === objectUuid) {
        // 更新选中的位置数据
        if (this.selectedPositionData) {
          this.selectedPositionData.xcoordinate = clampedPosition.x.toString()
          this.selectedPositionData.ycoordinate = clampedPosition.y.toString()
          this.selectedPositionData.zcoordinate = clampedPosition.z.toString()
        }
        // 强制Vue更新界面
        this.$forceUpdate()
      }
    },


    // 导入对象
    importObjects(objects) {
      objects.forEach(objData => {
        try {
          this.createObjectFromData(objData)
        } catch (error) {
          console.error(`导入对象失败 (uuid: ${objData.uuid}):`, error)
        }
      })
    },

    // 计算人物模型的缩放比例（根据地图分辨率）
    calculatePersonScale() {
      // 基准分辨率：0.05 米/像素（对应缩放比例 1.0）
      const baseResolution = 0.05

      // 获取当前地图的分辨率
      let currentResolution = baseResolution

      if (this.pgmRenderer && this.pgmRenderer.hasPGM()) {
        const bounds = this.pgmRenderer.getPGMBounds()
        if (bounds && bounds.resolution) {
          currentResolution = bounds.resolution
        }
      } else if (this.currentMapData && this.currentMapData.resolution) {
        // 如果没有渲染PGM但有地图数据，尝试从地图数据获取分辨率
        currentResolution = this.currentMapData.resolution
      }

      // 计算缩放比例：分辨率越小（越精细），人物应该越大
      const scale = baseResolution / currentResolution

      // 限制缩放范围，避免过大或过小
      const clampedScale = Math.max(0.5, Math.min(3.0, scale))

      console.log(`人物缩放计算 - 当前分辨率: ${currentResolution}, 基准分辨率: ${baseResolution}, 缩放比例: ${clampedScale}`)

      return clampedScale
    },

    // 更新现有位置元素的缩放比例
    updateExistingPersonScale() {
      const scale = this.calculatePersonScale()

      this.sceneObjects.forEach(obj => {
        if (obj.type === 'person' && obj.mesh) {
          // 移除旧的网格
          this.scene.remove(obj.mesh)

          // 更新缩放比例
          obj.scale = scale

          // 重新创建网格
          obj.createMesh()

          // 重新添加到场景
          this.scene.add(obj.mesh)

          // 如果是选中的对象，保持选中状态
          if (obj === this.selectedObject) {
            obj.setSelected(true)
          }
        }
      })

      console.log(`已更新 ${this.sceneObjects.filter(obj => obj.type === 'person').length} 个位置元素的缩放比例为: ${scale}`)
    },

    // 从数据创建对象
    createObjectFromData(objData) {
      // 创建三维向量的标准方法，表示坐标系原点位置的向量对象
      const position = new THREE.Vector3(objData.position.x, objData.position.y, objData.position.z)

      let object
      switch (objData.type) {
        case 'person': {
          // 计算适合当前地图分辨率的缩放比例
          const scale = this.calculatePersonScale()
          object = makeThreeObjectNonReactive(new Person(objData.name, objData.name, position, scale))
          // 设置uuid到Person对象中，用于userData
          object.uuid = objData.uuid || null
          if (objData.rotation !== undefined) {
            object.setRotation(objData.rotation)
          }
          // 保存业务信息到3D对象中
          object.actionNo = objData.actionNo || ''
          object.contentDetail = objData.contentDetail || ''
          // 保存状态标记
          object.isSaved = objData.isSaved || false
          break
        }
        default:
          console.warn('未知的对象类型:', objData.type)
          return
      }

      if (object && object.mesh) {
        this.scene.add(object.mesh)
        this.sceneObjects.push(object)
      }
    },

    // 清空场景（不显示确认对话框）
    clearSceneWithoutConfirm() {
      this.sceneObjects = SceneSetup.clearScene(this.scene, this.sceneObjects)
      this.selectedObject = null

      // 清理所有地图渲染（PCD和PGM）
      this.clearAllMapRendering()

      // 清空选中的位置数据
      this.selectedPositionData = null

      // 重置拖拽管理器边界
      if (this.dragManager) {
        this.dragManager.resetBounds()
      }

      // 恢复默认地板和网格（如果PGM被清理了）
      SceneSetup.showDefaultFloor(this.scene)

      console.log('场景清理完成')
    }
  }
}
</script>

<style scoped>
.map3d-container {
  display: flex;
  height: 100vh;
  width: 100%;
}

/* 第一列：地图列表面板 */
.map-list-panel {
  width: 250px;
  background: #fafafa;
  border-right: 1px solid #ddd;
  padding: 20px;
  overflow-y: auto;
}

.map-list-panel h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 5px;
}

/* 第二列：3D容器包装器 */
.three-container-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 操作区 */
.map-operations {
  background: #fff;
  border-bottom: 1px solid #ddd;
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.map-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 拖动位置元素样式 */
.drag-position-item {
  background: #f0f8ff;
  border: 2px dashed #1890ff;
  border-radius: 6px;
  padding: 6px 30px;
  cursor: grab;
  user-select: none;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
}

.drag-position-item:hover {
  background: #e6f7ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.drag-position-item:active {
  cursor: grabbing;
  transform: translateY(0);
}

/* 第二列：3D容器 */
.three-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 第三列：控制面板 */
.control-panel {
  width: 300px;
  background: #f5f5f5;
  border-left: 1px solid #ddd;
  padding: 20px;
  overflow-y: auto;
}

.control-panel h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}

.object-list {
  margin-bottom: 30px;
}

.object-item {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 8px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.object-item:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.object-item.selected {
  background: #e3f2fd;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.delete-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  transition: background-color 0.2s ease;
}

.delete-btn:hover {
  background: #c82333;
}

.object-properties {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
}

.empty-state {
  height: 100%;
  /* min-height: 300px; */
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.position-actions {
  margin-bottom: 15px;
  padding-bottom: 15px;
  /* border-bottom: 1px solid #eee; */
}

/* 属性行布局优化 */
.property-group {
  margin-bottom: 12px;
}

.property-group.inline {
  display: flex;
  align-items: center;
  gap: 8px;
}

.property-group.block {
  display: block;
}

.property-group.inline label {
  width: 66px;
  margin: 0;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.property-group.inline .ant-input,
.property-group.inline .ant-input-number {
  width: 140px;
}

.property-group.block .ant-input,
.property-group.block .ant-textarea {
  width: 100%;
}

.property-group input,
.property-group .ant-input-number {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  transition: border-color 0.2s ease;
}


.edit-btn,
.add-btn,
.move-btn,
.stop-btn,
.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin: 5px 5px 5px 0;
  transition: background-color 0.2s ease;
}

.edit-btn {
  background: #17a2b8;
  color: white;
}

.edit-btn:hover {
  background: #138496;
}

.add-btn {
  background: #28a745;
  color: white;
}

.add-btn:hover {
  background: #218838;
}

.move-btn {
  background: #007bff;
  color: white;
}

.move-btn:hover {
  background: #0056b3;
}



.move-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.stop-btn {
  background: #dc3545;
  color: white;
}

.stop-btn:hover {
  background: #c82333;
}

.action-btn {
  background: #6c757d;
  color: white;
  width: 100%;
  margin-bottom: 10px;
}

.action-btn:hover {
  background: #545b62;
}

.action-btn.danger {
  background: #dc3545;
}

.action-btn.danger:hover {
  background: #c82333;
}

.action-btn.export {
  background: #28a745;
}

.action-btn.export:hover {
  background: #218838;
}

.action-btn.import {
  background: #17a2b8;
}

.action-btn.import:hover {
  background: #138496;
}
</style>