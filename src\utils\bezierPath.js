/**
 * 贝塞尔曲线路径类
 */
import * as THREE from 'three'
import { BasePath, PATH_DIRECTIONS } from './pathSystem.js'

export class BezierPath extends BasePath {
  constructor(name, mapId, startPoint = { x: 0, y: 0, z: 0 }, endPoint = { x: 3, y: 0, z: 0 }) {
    super('bezier', name, mapId)
    
    // 路径点（限制在2D平面，y=0.1避免与地板重叠）
    this.startPoint = { x: startPoint.x, y: 0.1, z: startPoint.z }
    this.endPoint = { x: endPoint.x, y: 0.1, z: endPoint.z }

    // 控制点（默认在中间位置稍微偏移）
    const midX = (startPoint.x + endPoint.x) / 2
    const midZ = (startPoint.z + endPoint.z) / 2
    this.bezierControlPoints = [
      { x: midX, y: 0.1, z: midZ + 1 } // 默认一个控制点
    ]
    
    // 3D控制点对象
    this.startControlPoint = null
    this.endControlPoint = null
    this.bezierControlMeshes = []
    
    // 曲线对象
    this.curve = null
    
    this.createPath()
  }
  
  // 创建路径
  createPath() {
    this.group.clear()
    this.bezierControlMeshes = []
    
    // 创建曲线
    this.createCurve()
    
    // 创建控制点
    this.createControlPoints()
    
    // 创建方向指示器
    this.createDirectionIndicator()
  }
  
  // 创建曲线
  createCurve() {
    // 构建曲线点数组
    const curvePoints = [
      new THREE.Vector3(this.startPoint.x, this.startPoint.y, this.startPoint.z)
    ]

    // 添加贝塞尔控制点
    this.bezierControlPoints.forEach(point => {
      curvePoints.push(new THREE.Vector3(point.x, point.y, point.z))
    })

    // 添加终点
    curvePoints.push(new THREE.Vector3(this.endPoint.x, this.endPoint.y, this.endPoint.z))

    // 创建Catmull-Rom曲线（平滑插值）
    this.curve = new THREE.CatmullRomCurve3(curvePoints, false, 'centripetal', 0.5)



    // 根据路径方向调整曲线，为箭头留出空间
    let adjustedCurve = this.curve
    const arrowOffset = 0.125 // 箭头长度的一半

    if (this.direction === PATH_DIRECTIONS.UNIDIRECTIONAL) {
      // 单向：曲线在箭头底部结束
      adjustedCurve = this.createTrimmedCurve(this.curve, 0, 1 - arrowOffset / this.curve.getLength())
    } else if (this.direction === PATH_DIRECTIONS.BIDIRECTIONAL) {
      // 双向：两端都为箭头留出空间
      const totalLength = this.curve.getLength()
      const startTrim = arrowOffset / totalLength
      const endTrim = 1 - arrowOffset / totalLength
      adjustedCurve = this.createTrimmedCurve(this.curve, startTrim, endTrim)
    }

    // 创建2D风格的管道几何体 - 使用更扁平的管道
    const tubeRadius = 0.015 // 减小半径
    const radialSegments = 6 // 减少径向分段数，使其看起来更2D
    const tubeGeometry = new THREE.TubeGeometry(adjustedCurve, 100, tubeRadius, radialSegments, false)

    const material = new THREE.MeshBasicMaterial({
      color: this.getLineColor()
    })

    this.line = new THREE.Mesh(tubeGeometry, material)

    // 确保路径严格限制在2D平面上
    this.line.position.y = 0
    this.line.userData = {
      objectType: 'pathLine',
      pathUuid: this.uuid,
      clickable: true // 可点击添加控制点
    }

    this.group.add(this.line)
  }

  // 创建修剪后的曲线
  createTrimmedCurve(originalCurve, startT, endT) {
    // 获取修剪后的点
    const trimmedPoints = []
    const segments = 100

    for (let i = 0; i <= segments; i++) {
      const t = startT + (endT - startT) * (i / segments)
      const point = originalCurve.getPoint(Math.max(0, Math.min(1, t)))
      trimmedPoints.push(point)
    }

    return new THREE.CatmullRomCurve3(trimmedPoints, false, 'centripetal', 0.5)
  }
  
  // 创建控制点
  createControlPoints() {
    // 起点控制点
    this.startControlPoint = this.createControlPoint(
      this.startPoint,
      0x00ff00, // 绿色
      'startPoint'
    )
    
    // 终点控制点
    this.endControlPoint = this.createControlPoint(
      this.endPoint,
      0xff0000, // 红色
      'endPoint'
    )
    
    this.group.add(this.startControlPoint)
    this.group.add(this.endControlPoint)
    
    // 贝塞尔控制点
    this.bezierControlPoints.forEach((point, index) => {
      const controlMesh = this.createControlPoint(
        point,
        0xffff00, // 黄色
        'bezierControl',
        index
      )
      this.bezierControlMeshes.push(controlMesh)
      this.group.add(controlMesh)
    })
  }
  
  // 创建单个控制点
  createControlPoint(position, color, pointType, index = -1) {
    // 根据地图复杂度动态调整控制点大小，提高可选择性
    const baseRadius = 0.2
    const radius = baseRadius * (this.mapComplexity > 1000000 ? 1.5 : 1.0) // 大地图使用更大的控制点

    const geometry = new THREE.SphereGeometry(radius, 16, 16)
    const material = new THREE.MeshBasicMaterial({
      color: color,
      transparent: true,
      opacity: 0.8 // 半透明以减少视觉干扰
    })
    const sphere = new THREE.Mesh(geometry, material)

    // 确保控制点严格限制在2D平面上
    sphere.position.set(position.x, 0.1, position.z)
    sphere.userData = {
      draggable: true,
      objectType: 'pathControlPoint',
      pathUuid: this.uuid,
      pointType: pointType,
      pointIndex: index
    }

    return sphere
  }
  
  // 创建方向指示器
  createDirectionIndicator() {
    if (this.curve) {
      const points = this.curve.getPoints(50)
      
      if (this.direction === PATH_DIRECTIONS.UNIDIRECTIONAL) {
        // 在终点创建箭头
        const endPoint = points[points.length - 1]
        const beforeEndPoint = points[points.length - 5] || points[points.length - 2]
        const arrow = this.createArrow(endPoint, beforeEndPoint)
        this.group.add(arrow)
      } else {
        // 双向箭头
        const startPoint = points[0]
        const afterStartPoint = points[4] || points[1]
        const endPoint = points[points.length - 1]
        const beforeEndPoint = points[points.length - 5] || points[points.length - 2]
        
        const arrow1 = this.createArrow(endPoint, beforeEndPoint)
        const arrow2 = this.createArrow(startPoint, afterStartPoint)
        this.group.add(arrow1)
        this.group.add(arrow2)
      }
    }
  }
  
  // 创建2D风格的箭头
  createArrow(position, fromPosition) {
    const direction = new THREE.Vector3()
      .subVectors(position, fromPosition)
      .normalize()

    // 创建更扁平的箭头几何体
    const arrowGeometry = new THREE.ConeGeometry(0.08, 0.25, 6) // 减少分段数
    const arrowMaterial = new THREE.MeshBasicMaterial({
      color: this.getLineColor()
    })
    const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial)

    // 设置箭头位置，确保Y坐标严格限制在0.1
    arrow.position.set(position.x, 0.1, position.z)

    // 对于2D路径，箭头只需要绕Y轴旋转
    const angle = Math.atan2(direction.x, direction.z)
    arrow.rotation.set(Math.PI / 2, angle, 0) // 先绕X轴旋转90度让箭头水平，再绕Y轴旋转指向正确方向

    return arrow
  }
  
  // 获取线条颜色
  getLineColor() {
    if (this.isSelected) return 0xff0000 // 选中时红色
    if (this.isEditing) return 0xffff00 // 编辑时黄色
    return this.direction === PATH_DIRECTIONS.BIDIRECTIONAL ? 0x00ff00 : 0x9966ff // 紫色
  }

  // 获取线条宽度
  getLineWidth() {
    if (this.isSelected || this.isEditing) return 3
    return 2
  }

  // 更新外观
  updateAppearance() {
    if (this.line && this.line.material) {
      this.line.material.color.setHex(this.getLineColor())
    }
    // 重新创建路径以更新箭头颜色
    this.createPath()

    // 更新控制点可见性和颜色
    const visible = this.isSelected || this.isEditing
    if (this.startControlPoint) {
      this.startControlPoint.visible = visible
      // 选中状态时，控制点也变为红色
      if (this.isSelected) {
        this.startControlPoint.material.color.setHex(0xff0000) // 红色
      } else {
        this.startControlPoint.material.color.setHex(0x00ff00) // 绿色起点
      }
    }

    if (this.endControlPoint) {
      this.endControlPoint.visible = visible
      // 选中状态时，控制点也变为红色
      if (this.isSelected) {
        this.endControlPoint.material.color.setHex(0xff0000) // 红色
      } else {
        this.endControlPoint.material.color.setHex(0xff0000) // 红色终点
      }
    }

    this.bezierControlMeshes.forEach(mesh => {
      mesh.visible = visible
      // 选中状态时，贝塞尔控制点也变为红色
      if (this.isSelected) {
        mesh.material.color.setHex(0xff0000) // 红色
      } else {
        mesh.material.color.setHex(0xffff00) // 黄色贝塞尔控制点
      }
    })
  }
  
  // 更新控制点位置
  updateControlPoint(pointType, newPosition, pointIndex = -1) {
    // 限制在2D平面，y=0.1避免与地板重叠
    const position = { x: newPosition.x, y: 0.1, z: newPosition.z }
    
    if (pointType === 'startPoint') {
      this.startPoint = position
      if (this.startControlPoint) {
        this.startControlPoint.position.set(position.x, 0.1, position.z)
      }
    } else if (pointType === 'endPoint') {
      this.endPoint = position
      if (this.endControlPoint) {
        this.endControlPoint.position.set(position.x, 0.1, position.z)
      }
    } else if (pointType === 'bezierControl' && pointIndex >= 0 && pointIndex < this.bezierControlPoints.length) {
      this.bezierControlPoints[pointIndex] = position
      if (this.bezierControlMeshes[pointIndex]) {
        this.bezierControlMeshes[pointIndex].position.set(position.x, 0.1, position.z)
      }
    }
    
    // 重新创建曲线
    this.createPath()
  }
  
  // 添加控制点
  addControlPoint(position) {
    // 限制在2D平面，y=0.1避免与地板重叠
    const clickPoint = { x: position.x, y: 0.1, z: position.z }

    // 找到最佳插入位置，但使用双击的确切位置作为新控制点
    const insertIndex = this.findBestInsertIndex(clickPoint)

    // 直接使用双击位置作为新控制点，确保位置精确
    const newControlPoint = {
      x: clickPoint.x,
      y: 0.1,
      z: clickPoint.z
    }

    this.bezierControlPoints.splice(insertIndex, 0, newControlPoint)
    this.createPath()
  }

  // 找到插入新控制点的最佳索引位置
  findBestInsertIndex(clickPoint) {
    if (this.bezierControlPoints.length === 0) {
      return 0
    }

    // 如果当前曲线存在，使用曲线上的点来找最佳插入位置
    if (this.curve) {
      let minDistance = Infinity
      let bestT = 0

      // 在曲线上采样多个点，找到最接近双击位置的点的t值
      const sampleCount = 200
      for (let i = 0; i <= sampleCount; i++) {
        const t = i / sampleCount
        const pointOnCurve = this.curve.getPoint(t)

        const distance = Math.sqrt(
          Math.pow(pointOnCurve.x - clickPoint.x, 2) +
          Math.pow(pointOnCurve.z - clickPoint.z, 2)
        )

        if (distance < minDistance) {
          minDistance = distance
          bestT = t
        }
      }

      // 根据t值确定插入位置
      // t值表示在曲线上的相对位置，我们根据这个来确定在控制点数组中的插入位置
      let insertIndex

      if (this.bezierControlPoints.length === 1) {
        // 只有一个控制点的情况
        if (bestT < 0.5) {
          insertIndex = 0 // 插入到现有控制点之前
        } else {
          insertIndex = 1 // 插入到现有控制点之后
        }
      } else {
        // 多个控制点的情况，根据t值比例确定插入位置
        // t值越大，插入位置越靠后
        insertIndex = Math.round(bestT * this.bezierControlPoints.length)
        insertIndex = Math.max(0, Math.min(this.bezierControlPoints.length, insertIndex))
      }

      return insertIndex
    }

    // 如果没有曲线，返回中间位置
    return Math.floor(this.bezierControlPoints.length / 2)
  }


  
  // 删除控制点
  removeControlPoint(index) {
    if (index >= 0 && index < this.bezierControlPoints.length) {
      this.bezierControlPoints.splice(index, 1)
      this.createPath()
    }
  }
  
  // 获取路径数据（扩展基类）
  getPathData() {
    const baseData = super.getPathData()
    return {
      ...baseData,
      startPoint: { ...this.startPoint },
      endPoint: { ...this.endPoint },
      controlPoints: this.bezierControlPoints.map(p => ({ ...p }))
    }
  }
  
  // 更新属性（扩展基类）
  updateProperty(property, value) {
    super.updateProperty(property, value)

    // 处理特殊属性
    if (property === 'direction') {
      this.createPath() // 重新创建以更新方向指示器
    } else if (property.startsWith('startPoint.') || property.startsWith('endPoint.')) {
      const [pointType, coord] = property.split('.')
      if (pointType === 'startPoint') {
        this.startPoint[coord] = coord === 'y' ? 0.1 : parseFloat(value) || 0
        if (this.startControlPoint) {
          this.startControlPoint.position[coord] = coord === 'y' ? 0.1 : this.startPoint[coord]
        }
      } else if (pointType === 'endPoint') {
        this.endPoint[coord] = coord === 'y' ? 0.1 : parseFloat(value) || 0
        if (this.endControlPoint) {
          this.endControlPoint.position[coord] = coord === 'y' ? 0.1 : this.endPoint[coord]
        }
      }
      this.createPath()
    }
  }

  // 计算路径长度
  getLength() {
    if (this.curve) {
      return this.curve.getLength()
    }
    return 0
  }
}
