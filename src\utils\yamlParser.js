/**
 * YAML解析器工具模块
 * 处理ROS地图配置文件的解析
 */

/**
 * YAML解析器类
 */
export class YAMLParser {
  /**
   * 解析简单的YAML文件内容
   */
  static parseYAML(yamlText) {
    const result = {}
    const lines = yamlText.split('\n')
    
    for (const line of lines) {
      const trimmedLine = line.trim()
      
      // 跳过空行和注释
      if (!trimmedLine || trimmedLine.startsWith('#')) {
        continue
      }
      
      // 查找键值对
      const colonIndex = trimmedLine.indexOf(':')
      if (colonIndex === -1) {
        continue
      }
      
      const key = trimmedLine.substring(0, colonIndex).trim()
      const valueStr = trimmedLine.substring(colonIndex + 1).trim()
      
      // 解析值
      result[key] = this.parseValue(valueStr)
    }
    
    return result
  }
  
  /**
   * 解析YAML值
   */
  static parseValue(valueStr) {
    // 移除引号
    if ((valueStr.startsWith('"') && valueStr.endsWith('"')) ||
        (valueStr.startsWith("'") && valueStr.endsWith("'"))) {
      return valueStr.slice(1, -1)
    }
    
    // 解析数组
    if (valueStr.startsWith('[') && valueStr.endsWith(']')) {
      const arrayStr = valueStr.slice(1, -1)
      if (arrayStr.trim() === '') {
        return []
      }
      
      return arrayStr.split(',').map(item => {
        const trimmed = item.trim()
        const num = parseFloat(trimmed)
        return isNaN(num) ? trimmed : num
      })
    }
    
    // 解析布尔值
    if (valueStr === 'true') return true
    if (valueStr === 'false') return false
    
    // 解析数字
    const num = parseFloat(valueStr)
    if (!isNaN(num)) {
      return num
    }
    
    // 返回字符串
    return valueStr
  }
  
  /**
   * 加载并解析YAML文件
   */
  static async loadYAML(filePath) {
    try {
      const response = await fetch(filePath)
      if (!response.ok) {
        throw new Error(`无法加载YAML文件: ${response.status}`)
      }
      
      const yamlText = await response.text()
      return this.parseYAML(yamlText)
    } catch (error) {
      console.error('YAML文件加载失败:', error)
      throw error
    }
  }
  
  /**
   * 验证地图配置文件
   */
  static validateMapConfig(config) {
    const required = ['image', 'resolution', 'origin']
    const missing = required.filter(key => !(key in config))
    
    if (missing.length > 0) {
      throw new Error(`地图配置文件缺少必要字段: ${missing.join(', ')}`)
    }
    
    // 验证数据类型
    if (typeof config.resolution !== 'number' || config.resolution <= 0) {
      throw new Error('resolution必须是正数')
    }
    
    if (!Array.isArray(config.origin) || config.origin.length < 2) {
      throw new Error('origin必须是包含至少2个元素的数组')
    }
    
    return true
  }
  
  /**
   * 获取默认地图配置
   */
  static getDefaultMapConfig() {
    return {
      image: 'screen.pgm',
      mode: 'trinary',
      resolution: 0.025,
      origin: [0, 0, 0],
      negate: 0,
      occupied_thresh: 0.65,
      free_thresh: 0.25
    }
  }
}
