import * as THREE from 'three'

export class Person {
  constructor(id, name, position = new THREE.Vector3(0, 0, 0), scale = 1.0) {
    this.id = id
    this.name = name
    this.type = 'person'
    this.position = position.clone()
    this.isSelected = false
    this.rotation = 0 // 人物朝向角度（弧度），0表示面向+Z方向（正面朝前）
    this.scale = scale // 缩放比例，用于适应不同分辨率的地图

    this.createMesh()
    this.setupAnimation()
  }
  
  createMesh() {
    // 创建人物的3D模型（简化版，使用基本几何体）
    const group = new THREE.Group()

    // 根据缩放比例调整所有尺寸
    const s = this.scale

    // 身体（圆柱体）- 调整为真实人体比例
    const bodyGeometry = new THREE.CylinderGeometry(0.15 * s, 0.2 * s, 0.6 * s, 8)
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4a90e2 })
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial)
    body.position.y = 0.3 * s
    body.castShadow = true
    group.add(body)

    // 头部（球体）- 调整为合适大小
    const headGeometry = new THREE.SphereGeometry(0.12 * s, 8, 6)
    const headMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac })
    const head = new THREE.Mesh(headGeometry, headMaterial)
    head.position.y = 0.72 * s
    head.castShadow = true
    group.add(head)

    // 添加脸部特征（眼睛和鼻子）来区分正面和背面
    // 眼睛（两个小球体）- 调整位置和大小
    const eyeGeometry = new THREE.SphereGeometry(0.015 * s, 4, 4)
    const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 })

    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial)
    leftEye.position.set(-0.04 * s, 0.75 * s, 0.1 * s) // 左眼，调整到新的头部位置
    group.add(leftEye)

    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial)
    rightEye.position.set(0.04 * s, 0.75 * s, 0.1 * s) // 右眼
    group.add(rightEye)

    // 鼻子（小圆锥体）- 调整大小
    const noseGeometry = new THREE.ConeGeometry(0.01 * s, 0.03 * s, 4)
    const noseMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac })
    const nose = new THREE.Mesh(noseGeometry, noseMaterial)
    nose.position.set(0, 0.71 * s, 0.11 * s) // 鼻子在脸部前方，调整到新位置
    nose.rotation.x = Math.PI / 2 // 让鼻子朝前
    group.add(nose)

    // 左臂 - 调整大小和位置
    const armGeometry = new THREE.CylinderGeometry(0.04 * s, 0.04 * s, 0.4 * s, 6)
    const armMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac })

    const leftArm = new THREE.Mesh(armGeometry, armMaterial)
    leftArm.position.set(-0.22 * s, 0.4 * s, 0)
    leftArm.rotation.z = Math.PI / 6
    leftArm.castShadow = true
    group.add(leftArm)

    const rightArm = new THREE.Mesh(armGeometry, armMaterial)
    rightArm.position.set(0.22 * s, 0.4 * s, 0)
    rightArm.rotation.z = -Math.PI / 6
    rightArm.castShadow = true
    group.add(rightArm)
    
    // // 左腿 - 调整大小和位置
    // const legGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.4, 6)
    // const legMaterial = new THREE.MeshLambertMaterial({ color: 0x2c3e50 })

    // const leftLeg = new THREE.Mesh(legGeometry, legMaterial)
    // leftLeg.position.set(-0.08, -0.2, 0)
    // leftLeg.castShadow = true
    // group.add(leftLeg)

    // const rightLeg = new THREE.Mesh(legGeometry, legMaterial)
    // rightLeg.position.set(0.08, -0.2, 0)
    // rightLeg.castShadow = true
    // group.add(rightLeg)
    
    // 设置位置
    group.position.copy(this.position)
    
    // 添加用户数据
    group.userData = {
      draggable: true,
      objectType: 'person',
      objectUuid: this.uuid || this.id // 优先使用uuid，兼容旧的id
    }
    
    this.mesh = group
    this.bodyParts = {
      body,
      head,
      leftArm,
      rightArm,
      // leftLeg,
      // rightLeg,
      leftEye,
      rightEye,
      nose
    }
  }
  
  setupAnimation() {
    this.animationTime = 0
    this.walkCycle = {
      armSwing: 0,
      legSwing: 0
    }
  }
  
  update(deltaTime = 0.016) {
    this.animationTime += deltaTime

    // 更新网格位置
    if (this.mesh) {
      this.mesh.position.copy(this.position)
    }
  }
  

  
  setSelected(selected) {
    this.isSelected = selected

    if (this.mesh) {
      this.mesh.children.forEach(child => {
        if (child.material) {
          if (selected) {
            // 选中时设置为红色
            child.material.color.setHex(0xff0000)
            child.material.emissive.setHex(0x444444)
          } else {
            // 恢复原始颜色
            this.restoreOriginalColor(child)
            child.material.emissive.setHex(0x000000)
          }
        }
      })
    }
  }

  // 恢复原始颜色
  restoreOriginalColor(child) {
    // 根据身体部位恢复对应的原始颜色
    if (child === this.bodyParts.body) {
      child.material.color.setHex(0x4a90e2) // 蓝色身体
    } else if (child === this.bodyParts.head || child === this.bodyParts.leftArm ||
               child === this.bodyParts.rightArm || child === this.bodyParts.nose) {
      child.material.color.setHex(0xffdbac) // 肤色
    } else if (child === this.bodyParts.leftEye || child === this.bodyParts.rightEye) {
      child.material.color.setHex(0x000000) // 黑色眼睛
    }
  }
  
  updateProperty(property, value) {
    switch (property) {
      case 'name':
        this.name = value
        break
      case 'rotation':
        this.setRotation(value)
        break
    }
  }

  // 设置人物朝向（弧度）
  setRotation(rotation) {
    this.rotation = rotation
    if (this.mesh) {
      this.mesh.rotation.y = rotation
    }
  }

  // 设置人物朝向（角度）
  setRotationDegrees(degrees) {
    this.setRotation(degrees * Math.PI / 180)
  }

  // 获取人物朝向角度
  getRotationDegrees() {
    return this.rotation * 180 / Math.PI
  }
  
  // 获取边界框（用于碰撞检测）
  getBoundingBox() {
    const box = new THREE.Box3()
    if (this.mesh) {
      box.setFromObject(this.mesh)
    }
    return box
  }
  
  // 销毁对象
  dispose() {
    if (this.mesh) {
      this.mesh.children.forEach(child => {
        if (child.geometry) child.geometry.dispose()
        if (child.material) child.material.dispose()
      })
    }
  }
}
