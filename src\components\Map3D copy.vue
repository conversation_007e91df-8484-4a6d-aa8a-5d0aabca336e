<template>
  <div class="map3d-container">
    <div ref="threeContainer" class="three-container"></div>
    <div class="control-panel">
      <div class="drag-items">
        <h3>拖拽元素</h3>
        <div class="drag-item" draggable="true" @dragstart="startDrag('person', $event)">
          <span>👤 人物</span>
        </div>

        <div class="drag-item" draggable="true" @dragstart="startDrag('obstacle', $event)">
          <span>🧱 障碍物</span>
        </div>
      </div>
      
      <div class="object-list">
        <h3>场景对象</h3>
        <div v-for="obj in sceneObjects" :key="obj.id" class="object-item" 
             :class="{ selected: selectedObject?.id === obj.id }"
             @click="selectObject(obj)">
          <span>{{ getObjectIcon(obj.type) }} {{ obj.name }}</span>
          <button @click.stop="deleteObject(obj.id)" class="delete-btn">×</button>
        </div>
      </div>
      
      <div v-if="selectedObject" class="object-properties">
        <h3>属性编辑</h3>
        <div class="property-group">
          <label>名称:</label>
          <input v-model="selectedObject.name" @input="updateObjectProperty('name', $event.target.value)">
        </div>
        <div class="property-group">
          <label>X坐标:</label>
          <input type="number" :value="selectedObject.position?.x || 0"
                 @input="updateObjectPosition('x', parseFloat($event.target.value))">
        </div>
        <div class="property-group">
          <label>Y坐标:</label>
          <input type="number" :value="selectedObject.position?.y || 0"
                 @input="updateObjectPosition('y', parseFloat($event.target.value))">
        </div>
        <div class="property-group">
          <label>Z坐标:</label>
          <input type="number" :value="selectedObject.position?.z || 0"
                 @input="updateObjectPosition('z', parseFloat($event.target.value))">
        </div>

        <!-- 人物特有属性 -->
        <div v-if="selectedObject.type === 'person'" class="property-group">
          <label>朝向角度:</label>
          <input type="number" :value="Math.round(selectedObject.getRotationDegrees ? selectedObject.getRotationDegrees() : 0)"
                 @input="updateObjectProperty('rotation', parseFloat($event.target.value) * Math.PI / 180)"
                 min="0" max="360" step="15">
          <small style="display: block; color: #666; margin-top: 5px;">
            0°=正面朝前, 90°=朝右, 180°=朝后, 270°=朝左
          </small>
        </div>

        <!-- 障碍物特有属性 -->
        <div v-if="selectedObject.type === 'obstacle'" class="property-group">
          <label>宽度:</label>
          <input type="number" :value="selectedObject.width" 
                 @input="updateObjectProperty('width', parseFloat($event.target.value))">
        </div>
        <div v-if="selectedObject.type === 'obstacle'" class="property-group">
          <label>高度:</label>
          <input type="number" :value="selectedObject.height" 
                 @input="updateObjectProperty('height', parseFloat($event.target.value))">
        </div>
        <div v-if="selectedObject.type === 'obstacle'" class="property-group">
          <label>深度:</label>
          <input type="number" :value="selectedObject.depth" 
                 @input="updateObjectProperty('depth', parseFloat($event.target.value))">
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <button @click="duplicateSelectedObject" class="action-btn" v-if="selectedObject">
          复制对象
        </button>
        <button @click="clearScene" class="action-btn danger">
          清空场景
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { Person } from '../classes/Person.js'
import { Obstacle } from '../classes/Obstacle.js'
import { DragManager } from '../managers/DragManager.js'
import { markRaw } from 'vue'
import { createNonReactiveThreeObject, makeThreeObjectNonReactive } from '../utils/threeUtils.js'

export default {
  name: 'Map3D',
  data() {
    return {
      scene: null,
      camera: null,
      renderer: null,
      controls: null,
      dragManager: null,
      sceneObjects: [],
      selectedObject: null,
      nextId: 1
    }
  },

  mounted() {
    this.initThreeJS()
    this.setupDragAndDrop()
    this.animate()
  },
  beforeUnmount() {
    if (this.renderer) {
      this.renderer.dispose()
    }
  },
  methods: {
    initThreeJS() {
      // 创建场景
      this.scene = createNonReactiveThreeObject(THREE.Scene)
      this.scene.background = new THREE.Color(0xf0f0f0)

      // 创建相机
      const container = this.$refs.threeContainer
      this.camera = createNonReactiveThreeObject(THREE.PerspectiveCamera,
        75,
        container.clientWidth / container.clientHeight,
        0.1,
        1000
      )
      this.camera.position.set(10, 10, 10)

      // 创建渲染器
      this.renderer = createNonReactiveThreeObject(THREE.WebGLRenderer, { antialias: true })
      this.renderer.setSize(container.clientWidth, container.clientHeight)

      this.renderer.shadowMap.enabled = true
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
      container.appendChild(this.renderer.domElement)

      // 添加控制器
      this.controls = makeThreeObjectNonReactive(new OrbitControls(this.camera, this.renderer.domElement))
      this.controls.enableDamping = true
      this.controls.dampingFactor = 0.05
      
      // 添加光源
      this.setupLighting()
      
      // 添加地面
      this.createFloor()
      
      // 初始化拖拽管理器
      this.dragManager = markRaw(new DragManager(this.scene, this.camera, this.renderer.domElement))
      this.dragManager.setControls(this.controls)
      this.dragManager.setObjectPositionUpdateCallback(this.onDraggedObjectPositionUpdate)

      // 监听窗口大小变化
      window.addEventListener('resize', this.onWindowResize)

      // 监听点击事件
      this.renderer.domElement.addEventListener('click', this.onCanvasClick)
    },
    
    setupLighting() {
      // 环境光
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
      this.scene.add(ambientLight)
      
      // 方向光
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
      directionalLight.position.set(10, 10, 5)
      directionalLight.castShadow = true
      directionalLight.shadow.mapSize.width = 2048
      directionalLight.shadow.mapSize.height = 2048
      this.scene.add(directionalLight)
    },
    
    // 创建地板
    createFloor() {
      const geometry = new THREE.PlaneGeometry(30, 30)
      const material = new THREE.MeshLambertMaterial({
        color: 0xcccccc,
        transparent: true,
        opacity: 0.8
      })
      const floor = new THREE.Mesh(geometry, material)
      floor.rotation.x = -Math.PI / 2
      floor.receiveShadow = true
      this.scene.add(floor)

      // 添加网格
      const gridHelper = new THREE.GridHelper(30, 30, 0x888888, 0xaaaaaa)
      this.scene.add(gridHelper)

      // 添加坐标轴辅助器（可选，用于调试）
      const axesHelper = new THREE.AxesHelper(5)
      this.scene.add(axesHelper)
      // 红色=X轴(左右), 绿色=Y轴(上下), 蓝色=Z轴(前后)
    },

    setupDragAndDrop() {
      const container = this.$refs.threeContainer

      container.addEventListener('dragover', (e) => {
        e.preventDefault()
      })

      container.addEventListener('drop', (e) => {
        e.preventDefault()
        const objectType = e.dataTransfer.getData('text/plain')
        this.createObjectAtPosition(objectType, e)
      })
    },

    startDrag(type, event) {
      event.dataTransfer.setData('text/plain', type)
    },

    createObjectAtPosition(type, event) {
      const rect = this.$refs.threeContainer.getBoundingClientRect()
      const mouse = new THREE.Vector2()
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      const raycaster = new THREE.Raycaster()
      raycaster.setFromCamera(mouse, this.camera)

      // 与地面相交
      const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0)
      const intersectPoint = new THREE.Vector3()

      // 检查射线与平面的交点
      const intersection = raycaster.ray.intersectPlane(plane, intersectPoint)

      // 如果没有交点，使用默认位置
      if (!intersection) {
        console.warn('No intersection with ground plane, using default position')
        intersectPoint.set(0, 0, 0)
      }

      // 验证intersectPoint是有效的
      if (!intersectPoint || typeof intersectPoint.x !== 'number' || isNaN(intersectPoint.x)) {
        console.warn('Invalid intersect point, using default position')
        intersectPoint.set(0, 0, 0)
      }

      this.createObject(type, intersectPoint)
    },

    createObject(type, position) {
      // 验证position参数
      if (!position || typeof position.x !== 'number' || typeof position.y !== 'number' || typeof position.z !== 'number') {
        console.error('createObject: Invalid position parameter:', position)
        position = new THREE.Vector3(0, 0, 0)
      }

      let object
      const id = this.nextId++
      const name = `${this.getObjectTypeName(type)}${id}`

      try {
        switch (type) {
          case 'person':
            object = makeThreeObjectNonReactive(new Person(id, name, position))
            break
          case 'obstacle':
            object = makeThreeObjectNonReactive(new Obstacle(id, name, position, 1, 1, 1))
            break
          default:
            console.warn('createObject: Unknown object type:', type)
            return
        }

        if (!object || !object.mesh) {
          console.error('createObject: Failed to create object or mesh')
          return
        }

        this.scene.add(object.mesh)
        this.sceneObjects.push(object)
        this.selectedObject = object

        // Object created successfully
      } catch (error) {
        console.error(`Error creating ${type} object:`, error)
      }
    },

    selectObject(object) {
      // 取消之前选中对象的高亮
      if (this.selectedObject && this.selectedObject.setSelected) {
        this.selectedObject.setSelected(false)
      }

      this.selectedObject = object

      // 高亮当前选中对象
      if (object && object.setSelected) {
        object.setSelected(true)
      }

      // 强制更新Vue界面
      this.$forceUpdate()
    },

    deleteObject(id) {
      const index = this.sceneObjects.findIndex(obj => obj.id === id)
      if (index !== -1) {
        const object = this.sceneObjects[index]
        this.scene.remove(object.mesh)
        this.sceneObjects.splice(index, 1)

        if (this.selectedObject && this.selectedObject.id === id) {
          this.selectedObject = null
        }
      }
    },

    updateObjectProperty(property, value) {
      if (this.selectedObject && this.selectedObject.updateProperty) {
        this.selectedObject.updateProperty(property, value)
      }
    },

    updateObjectPosition(axis, value) {
      if (this.selectedObject && this.selectedObject.position) {
        this.selectedObject.position[axis] = value

        // 根据对象类型进行不同的处理
        if (this.selectedObject.type === 'path') {
          // 对于路径，移动第一个点
          if (this.selectedObject.points && this.selectedObject.points.length > 0) {
            this.selectedObject.points[0][axis] = value
            // 重新创建路径
            this.selectedObject.updatePathLine()
            this.selectedObject.createControlPoints()
          }
        } else if (this.selectedObject.type === 'obstacle') {
          // 对于障碍物，使用updatePosition方法
          if (this.selectedObject.updatePosition) {
            this.selectedObject.updatePosition(this.selectedObject.position)
          } else if (this.selectedObject.mesh) {
            this.selectedObject.mesh.position[axis] = value
          }
        } else {
          // 对于人物和其他对象
          if (this.selectedObject.mesh) {
            this.selectedObject.mesh.position[axis] = value
          }
        }
      }
    },

    getObjectIcon(type) {
      const icons = {
        person: '👤',
        obstacle: '🧱'
      }
      return icons[type] || '❓'
    },

    getObjectTypeName(type) {
      const names = {
        person: '人物',
        obstacle: '障碍物'
      }
      return names[type] || '对象'
    },

    onCanvasClick(event) {
      const rect = this.$refs.threeContainer.getBoundingClientRect()
      const mouse = new THREE.Vector2()
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

      const raycaster = new THREE.Raycaster()
      raycaster.setFromCamera(mouse, this.camera)

      // 递归检测所有子对象，包括Group内部的mesh
      const intersects = raycaster.intersectObjects(
        this.sceneObjects.map(obj => obj.mesh).filter(mesh => mesh),
        true // 递归检测子对象
      )

      if (intersects.length > 0) {
        const clickedMesh = intersects[0].object

        // 查找对应的场景对象，需要考虑Group结构
        let clickedObject = null

        // 首先尝试直接匹配
        clickedObject = this.sceneObjects.find(obj => obj.mesh === clickedMesh)

        // 如果没找到，可能是Group内部的子对象，向上查找
        if (!clickedObject) {
          let parent = clickedMesh.parent
          while (parent && !clickedObject) {
            clickedObject = this.sceneObjects.find(obj => obj.mesh === parent)
            parent = parent.parent
          }
        }

        // 还可以通过userData查找
        if (!clickedObject) {
          // 检查点击的mesh的userData
          if (clickedMesh.userData && clickedMesh.userData.objectType && clickedMesh.userData.objectId) {
            const { objectType, objectId } = clickedMesh.userData
            clickedObject = this.sceneObjects.find(obj =>
              obj.type === objectType && obj.id === objectId
            )
          }

          // 检查父级的userData
          if (!clickedObject && clickedMesh.parent && clickedMesh.parent.userData) {
            const { objectType, objectId } = clickedMesh.parent.userData
            if (objectType && objectId) {
              clickedObject = this.sceneObjects.find(obj =>
                obj.type === objectType && obj.id === objectId
              )
            }
          }
        }

        if (clickedObject) {
          this.selectObject(clickedObject)
        } else {
          this.selectObject(null)
        }
      } else {
        this.selectObject(null)
      }
    },

    onWindowResize() {
      const container = this.$refs.threeContainer
      this.camera.aspect = container.clientWidth / container.clientHeight
      this.camera.updateProjectionMatrix()
      this.renderer.setSize(container.clientWidth, container.clientHeight)
    },

    // 持续更新渲染
    animate() {
      requestAnimationFrame(this.animate)

      // 更新相机控制器
      this.controls.update()

      // 更新所有场景对象
      this.sceneObjects.forEach(obj => {
        if (obj.update) {
          obj.update()
        }
      })

      // 渲染场景
      this.renderer.render(this.scene, this.camera)
    },



    // 复制对象
    duplicateSelectedObject() {
      if (!this.selectedObject) return

      const offset = new THREE.Vector3(1, 0, 1)
      const newPosition = this.selectedObject.position.clone().add(offset)

      switch (this.selectedObject.type) {
        case 'person': {
          this.createObject('person', newPosition)
          break
        }
        case 'obstacle': {
          const obstacle = this.selectedObject
          const newObstacle = makeThreeObjectNonReactive(new Obstacle(
            this.nextId++,
            `${obstacle.name}_copy`,
            newPosition,
            obstacle.width,
            obstacle.height,
            obstacle.depth
          ))
          this.scene.add(newObstacle.mesh)
          this.sceneObjects.push(newObstacle)
          this.selectedObject = newObstacle
          break
        }

      }
    },

    // 清空场景
    clearScene() {
      if (confirm('确定要清空整个场景吗？此操作不可撤销。')) {
        // 移除所有对象
        this.sceneObjects.forEach(obj => {
          this.scene.remove(obj.mesh)
          if (obj.dispose) {
            obj.dispose()
          }
        })

        this.sceneObjects = []
        this.selectedObject = null
        this.nextId = 1
      }
    },

    // 拖拽对象位置更新回调
    onDraggedObjectPositionUpdate(objectType, objectId, newPosition) {
      // 找到对应的对象
      const object = this.sceneObjects.find(obj => obj.id === objectId && obj.type === objectType)
      if (!object) {
        return
      }

      // 更新对象的内部位置
      object.position.copy(newPosition)

      // 根据对象类型进行特殊处理
      switch (objectType) {
        case 'person':
          // 人物对象：确保mesh位置同步
          if (object.mesh) {
            object.mesh.position.copy(newPosition)
          }
          break



        case 'obstacle':
          // 障碍物对象：使用其updatePosition方法
          if (object.updatePosition) {
            object.updatePosition(newPosition)
          } else {
            // 备用方案：直接移动mesh
            if (object.mesh) {
              object.mesh.position.copy(newPosition)
            }
          }
          break
      }

      // 如果当前选中的是这个对象，触发界面更新
      if (this.selectedObject && this.selectedObject.id === objectId) {
        // 强制Vue更新界面
        this.$forceUpdate()
      }
    }
  }
}
</script>

<style scoped>
.map3d-container {
  display: flex;
  height: 100vh;
  width: 100%;
}

.three-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.control-panel {
  width: 300px;
  background: #f5f5f5;
  border-left: 1px solid #ddd;
  padding: 20px;
  overflow-y: auto;
}

.control-panel h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}

.drag-items {
  margin-bottom: 30px;
}

.drag-item {
  background: #fff;
  border: 2px dashed #007bff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  cursor: grab;
  text-align: center;
  transition: all 0.3s ease;
}

.drag-item:hover {
  background: #e3f2fd;
  border-color: #0056b3;
  transform: translateY(-2px);
}

.drag-item:active {
  cursor: grabbing;
}

.drag-item span {
  font-size: 14px;
  font-weight: 500;
  color: #007bff;
}

.object-list {
  margin-bottom: 30px;
}

.object-item {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 8px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.object-item:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.object-item.selected {
  background: #e3f2fd;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.delete-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  transition: background-color 0.2s ease;
}

.delete-btn:hover {
  background: #c82333;
}

.object-properties {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
}

.property-group {
  margin-bottom: 15px;
}

.property-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.property-group input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.property-group input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.property-group input[type="color"] {
  height: 40px;
  padding: 2px;
}

.edit-btn, .add-btn, .move-btn, .stop-btn, .action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin: 5px 5px 5px 0;
  transition: background-color 0.2s ease;
}

.edit-btn {
  background: #17a2b8;
  color: white;
}

.edit-btn:hover {
  background: #138496;
}

.add-btn {
  background: #28a745;
  color: white;
}

.add-btn:hover {
  background: #218838;
}

.move-btn {
  background: #007bff;
  color: white;
}

.move-btn:hover {
  background: #0056b3;
}

.move-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.stop-btn {
  background: #dc3545;
  color: white;
}

.stop-btn:hover {
  background: #c82333;
}

.action-buttons {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ddd;
}

.action-btn {
  background: #6c757d;
  color: white;
  width: 100%;
  margin-bottom: 10px;
}

.action-btn:hover {
  background: #545b62;
}

.action-btn.danger {
  background: #dc3545;
}

.action-btn.danger:hover {
  background: #c82333;
}
</style>
