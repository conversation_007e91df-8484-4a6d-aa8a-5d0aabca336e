/**
 * 直线路径类
 */
import * as THREE from 'three'
import { BasePath, PATH_DIRECTIONS } from './pathSystem.js'

export class StraightPath extends BasePath {
  constructor(name, mapId, startPoint = { x: 0, y: 0, z: 0 }, endPoint = { x: 2, y: 0, z: 0 }) {
    super('straight', name, mapId)
    
    // 路径点（限制在2D平面，y=0.1避免与地板重叠）
    this.startPoint = { x: startPoint.x, y: 0.1, z: startPoint.z }
    this.endPoint = { x: endPoint.x, y: 0.1, z: endPoint.z }
    
    // 控制点对象（用于拖拽）
    this.startControlPoint = null
    this.endControlPoint = null
    
    this.createPath()
  }
  
  // 创建路径
  createPath() {
    this.group.clear()
    
    // 创建线条
    this.createLine()
    
    // 创建控制点
    this.createControlPoints()
    
    // 创建方向指示器
    this.createDirectionIndicator()
  }
  
  // 创建线条
  createLine() {
    const startPoint = new THREE.Vector3(this.startPoint.x, this.startPoint.y, this.startPoint.z)
    const endPoint = new THREE.Vector3(this.endPoint.x, this.endPoint.y, this.endPoint.z)

    // 计算方向和距离
    const direction = new THREE.Vector3().subVectors(endPoint, startPoint)

    direction.normalize()

    // 根据路径方向调整线条长度，为箭头留出空间
    let adjustedStartPoint = startPoint.clone()
    let adjustedEndPoint = endPoint.clone()
    const arrowLength = 0.125 // 箭头长度的一半

    if (this.direction === PATH_DIRECTIONS.UNIDIRECTIONAL) {
      // 单向：线条在箭头底部结束
      adjustedEndPoint = endPoint.clone().sub(direction.clone().multiplyScalar(arrowLength))
    } else if (this.direction === PATH_DIRECTIONS.BIDIRECTIONAL) {
      // 双向：两端都为箭头留出空间
      adjustedStartPoint = startPoint.clone().add(direction.clone().multiplyScalar(arrowLength))
      adjustedEndPoint = endPoint.clone().sub(direction.clone().multiplyScalar(arrowLength))
    }

    // 计算调整后的距离和中心点
    const adjustedDistance = adjustedStartPoint.distanceTo(adjustedEndPoint)
    const center = new THREE.Vector3().addVectors(adjustedStartPoint, adjustedEndPoint).multiplyScalar(0.5)

    // 创建2D路径几何体 - 使用扁平的BoxGeometry而不是CylinderGeometry
    const lineWidth = 0.04 // 线条宽度
    const lineHeight = 0.01 // 线条高度（很薄，保持2D效果）
    const geometry = new THREE.BoxGeometry(lineWidth, lineHeight, adjustedDistance)

    const material = new THREE.MeshBasicMaterial({
      color: this.getLineColor()
    })

    this.line = new THREE.Mesh(geometry, material)

    // 设置位置
    this.line.position.copy(center)
    // 确保Y坐标严格限制在0.1
    this.line.position.y = 0.1

    // 设置旋转，让路径指向正确方向
    // 对于2D路径，只需要绕Y轴旋转
    const angle = Math.atan2(direction.x, direction.z)
    this.line.rotation.set(0, angle, 0)

    this.line.userData = {
      objectType: 'pathLine',
      pathUuid: this.uuid
    }

    this.group.add(this.line)
  }
  
  // 创建控制点
  createControlPoints() {
    // 起点控制点
    this.startControlPoint = this.createControlPoint(
      this.startPoint,
      0x00ff00, // 绿色
      'startPoint'
    )
    
    // 终点控制点
    this.endControlPoint = this.createControlPoint(
      this.endPoint,
      0xff0000, // 红色
      'endPoint'
    )
    
    this.group.add(this.startControlPoint)
    this.group.add(this.endControlPoint)
  }
  
  // 创建单个控制点
  createControlPoint(position, color, pointType) {
    // 根据地图复杂度动态调整控制点大小，提高可选择性
    const baseRadius = 0.2
    const radius = baseRadius * (this.mapComplexity > 1000000 ? 1.5 : 1.0) // 大地图使用更大的控制点

    const geometry = new THREE.SphereGeometry(radius, 16, 16)
    const material = new THREE.MeshBasicMaterial({
      color: color,
      transparent: true,
      opacity: 0.8 // 半透明以减少视觉干扰
    })
    const sphere = new THREE.Mesh(geometry, material)

    // 确保控制点严格限制在2D平面上
    sphere.position.set(position.x, 0.1, position.z)
    sphere.userData = {
      draggable: true,
      objectType: 'pathControlPoint',
      pathUuid: this.uuid,
      pointType: pointType
    }

    return sphere
  }
  
  // 创建方向指示器
  createDirectionIndicator() {
    if (this.direction === PATH_DIRECTIONS.UNIDIRECTIONAL) {
      // 在终点创建箭头
      const arrow = this.createArrow(this.endPoint, this.startPoint)
      this.group.add(arrow)
    } else {
      // 双向箭头
      const arrow1 = this.createArrow(this.endPoint, this.startPoint)
      const arrow2 = this.createArrow(this.startPoint, this.endPoint)
      this.group.add(arrow1)
      this.group.add(arrow2)
    }
  }
  
  // 创建2D风格的箭头
  createArrow(position, fromPosition) {
    const direction = new THREE.Vector3()
      .subVectors(
        new THREE.Vector3(position.x, position.y, position.z),
        new THREE.Vector3(fromPosition.x, fromPosition.y, fromPosition.z)
      )
      .normalize()

    // 创建更扁平的箭头几何体
    const arrowGeometry = new THREE.ConeGeometry(0.08, 0.25, 6) // 减少分段数
    const arrowMaterial = new THREE.MeshBasicMaterial({
      color: this.getLineColor()
    })
    const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial)

    // 设置箭头位置，确保Y坐标严格限制在0.1
    arrow.position.set(position.x, 0.1, position.z)

    // 箭头沿着路径方向指向
    const angle = Math.atan2(direction.x, direction.z)
    arrow.rotation.set(Math.PI / 2, angle, 0) // 先绕X轴旋转90度让箭头水平，再绕Y轴旋转指向路径方向

    return arrow
  }
  
  // 获取线条颜色
  getLineColor() {
    if (this.isSelected) return 0xff0000 // 选中时红色
    if (this.isEditing) return 0xffff00 // 编辑时黄色
    // return this.direction === PATH_DIRECTIONS.BIDIRECTIONAL ? 0x00ff00 : 0x0066ff
    return 0x00ff00 // 统一为绿色
  }

  // 获取线条宽度
  getLineWidth() {
    if (this.isSelected || this.isEditing) return 3
    return 2
  }

  // 更新外观
  updateAppearance() {
    if (this.line && this.line.material) {
      this.line.material.color.setHex(this.getLineColor())
    }
    // 重新创建路径以更新箭头颜色
    this.createPath()

    // 更新控制点可见性和颜色
    if (this.startControlPoint && this.endControlPoint) {
      const visible = this.isSelected || this.isEditing
      this.startControlPoint.visible = visible
      this.endControlPoint.visible = visible

      // 选中状态时，控制点也变为红色
      if (this.isSelected) {
        this.startControlPoint.material.color.setHex(0xff0000) // 红色
        this.endControlPoint.material.color.setHex(0xff0000) // 红色
      } else {
        // 恢复原始颜色
        this.startControlPoint.material.color.setHex(0x00ff00) // 绿色起点
        this.endControlPoint.material.color.setHex(0xff0000) // 红色终点
      }
    }
  }
  
  // 更新控制点位置
  updateControlPoint(pointType, newPosition) {
    // 限制在2D平面，y=0.1避免与地板重叠
    const position = { x: newPosition.x, y: 0.1, z: newPosition.z }
    
    if (pointType === 'startPoint') {
      this.startPoint = position
      if (this.startControlPoint) {
        this.startControlPoint.position.set(position.x, 0.1, position.z)
      }
    } else if (pointType === 'endPoint') {
      this.endPoint = position
      if (this.endControlPoint) {
        this.endControlPoint.position.set(position.x, 0.1, position.z)
      }
    }
    
    // 重新创建线条和方向指示器
    this.createPath()
  }
  
  // 获取路径数据（扩展基类）
  getPathData() {
    const baseData = super.getPathData()
    return {
      ...baseData,
      startPoint: { ...this.startPoint },
      endPoint: { ...this.endPoint }
    }
  }
  
  // 更新属性（扩展基类）
  updateProperty(property, value) {
    super.updateProperty(property, value)
    
    // 处理特殊属性
    if (property === 'direction') {
      this.createPath() // 重新创建以更新方向指示器
    } else if (property.startsWith('startPoint.') || property.startsWith('endPoint.')) {
      const [pointType, coord] = property.split('.')
      if (pointType === 'startPoint') {
        this.startPoint[coord] = coord === 'y' ? 0.1 : parseFloat(value) || 0
        if (this.startControlPoint) {
          this.startControlPoint.position[coord] = coord === 'y' ? 0.1 : this.startPoint[coord]
        }
      } else if (pointType === 'endPoint') {
        this.endPoint[coord] = coord === 'y' ? 0.1 : parseFloat(value) || 0
        if (this.endControlPoint) {
          this.endControlPoint.position[coord] = coord === 'y' ? 0.1 : this.endPoint[coord]
        }
      }
      this.createPath()
    }
  }
  
  // 计算路径长度
  getLength() {
    const dx = this.endPoint.x - this.startPoint.x
    const dz = this.endPoint.z - this.startPoint.z
    return Math.sqrt(dx * dx + dz * dz)
  }
}
