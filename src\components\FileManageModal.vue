<template>
  <a-modal
    :visible="visible"
    title="文件管理"
    :maskClosable="false"
    @cancel="handleCancel"
    :footer="null"
    width="800px"
  >
    <div class="file-manage-container">
      <a-tabs v-model:activeKey="activeFileTab" type="card">
        <!-- PGM文件 -->
        <a-tab-pane key="pgm" tab="PGM文件">
          <div class="file-section">
            <div class="file-upload-area">
              <a-upload
                v-model:file-list="pgmFileList"
                :before-upload="(file) => handleBeforeUpload(file, 'pgm')"
                @remove="(file) => handleRemoveFile(file, 'pgm')"
                accept=".pgm"
                :max-count="1"
              >
                <a-button type="primary">
                  <upload-outlined /> 选择PGM文件
                </a-button>
              </a-upload>
              <a-button 
                type="primary" 
                @click="handleUploadFiles('pgm')" 
                :loading="uploading.pgm"
                :disabled="pgmFileList.length === 0" 
                style="margin-left: 8px;"
              >
                上传文件
              </a-button>
            </div>
            <div class="file-list">
              <h4>当前文件</h4>
              <a-list :data-source="currentFiles.pgm" size="small">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <template #actions>
                      <a @click="handleDownloadFile(item)">下载</a>
                    </template>
                    <a-list-item-meta>
                      <template #title>{{ item }}</template>
                      <template #avatar>
                        <file-outlined />
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </div>
        </a-tab-pane>

        <!-- PCD文件 -->
        <a-tab-pane key="pcd" tab="PCD文件">
          <div class="file-section">
            <div class="file-upload-area">
              <a-upload
                v-model:file-list="pcdFileList"
                :before-upload="(file) => handleBeforeUpload(file, 'pcd')"
                @remove="(file) => handleRemoveFile(file, 'pcd')"
                accept=".pcd"
                :max-count="1"
              >
                <a-button type="primary">
                  <upload-outlined /> 选择PCD文件
                </a-button>
              </a-upload>
              <a-button 
                type="primary" 
                @click="handleUploadFiles('pcd')" 
                :loading="uploading.pcd"
                :disabled="pcdFileList.length === 0" 
                style="margin-left: 8px;"
              >
                上传文件
              </a-button>
            </div>
            <div class="file-list">
              <h4>当前文件</h4>
              <a-list :data-source="currentFiles.pcd" size="small">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <template #actions>
                      <a @click="handleDownloadFile(item)">下载</a>
                    </template>
                    <a-list-item-meta>
                      <template #title>{{ item }}</template>
                      <template #avatar>
                        <file-outlined />
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </div>
        </a-tab-pane>

        <!-- YAML文件 -->
        <a-tab-pane key="yaml" tab="YAML文件">
          <div class="file-section">
            <div class="file-upload-area">
              <a-upload
                v-model:file-list="yamlFileList"
                :before-upload="(file) => handleBeforeUpload(file, 'yaml')"
                @remove="(file) => handleRemoveFile(file, 'yaml')"
                accept=".yaml"
                :max-count="1"
              >
                <a-button type="primary">
                  <upload-outlined /> 选择YAML文件
                </a-button>
              </a-upload>
              <a-button 
                type="primary" 
                @click="handleUploadFiles('yaml')" 
                :loading="uploading.yaml"
                :disabled="yamlFileList.length === 0" 
                style="margin-left: 8px;"
              >
                上传文件
              </a-button>
            </div>
            <div class="file-list">
              <h4>当前文件</h4>
              <a-list :data-source="currentFiles.yaml" size="small">
                <template #renderItem="{ item }">
                  <a-list-item>
                    <template #actions>
                      <a @click="handleDownloadFile(item)">下载</a>
                    </template>
                    <a-list-item-meta>
                      <template #title>{{ item }}</template>
                      <template #avatar>
                        <file-outlined />
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>

<script>
import { UploadOutlined, FileOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { showLoading, hideLoading, requestSuccess, requestFailed } from '../utils/common.js'
import { downloadMapFile, uploadMapFile } from '../api/map.js'

export default {
  name: 'FileManageModal',
  components: {
    UploadOutlined,
    FileOutlined
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentMapId: {
      type: [String, Number],
      default: null
    },
    currentMapData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'files-uploaded'],
  data() {
    return {
      activeFileTab: 'pgm',
      pgmFileList: [],
      pcdFileList: [],
      yamlFileList: [],
      uploading: {
        pgm: false,
        pcd: false,
        yaml: false
      },
      currentFiles: {
        pgm: [],
        pcd: [],
        yaml: []
      },
      // 存储上传后的新文件名
      uploadedFileNames: {
        pgm: null,
        pcd: null,
        yaml: null
      },
      // 跟踪本次模态框会话是否有文件上传
      hasFilesUploadedInCurrentSession: false
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initModal()
      }
    },
    currentMapData: {
      handler() {
        this.initCurrentFilesList()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始化模态框
    initModal() {
      // 重置本次会话的文件上传标记
      this.hasFilesUploadedInCurrentSession = false
      // 初始化当前文件列表
      this.initCurrentFilesList()
    },

    // 初始化当前文件列表
    initCurrentFilesList() {
      if (this.currentMapData) {
        // 根据当前地图数据设置文件列表
        this.currentFiles.pgm = this.currentMapData.pgmName ? [this.currentMapData.pgmName] : []
        this.currentFiles.pcd = this.currentMapData.pcdName ? [this.currentMapData.pcdName] : []
        this.currentFiles.yaml = this.currentMapData.yamlName ? [this.currentMapData.yamlName] : []
      }
    },

    // 处理模态框取消
    async handleCancel() {
      // 只有在本次会话中有文件上传才通知父组件重新渲染
      if (this.hasFilesUploadedInCurrentSession) {
        console.log('检测到本次会话有文件上传，关闭文件管理模态框时通知父组件重新渲染地图')
        this.$emit('files-uploaded', this.uploadedFileNames)
      }

      this.$emit('update:visible', false)
      // 清空文件列表
      this.pgmFileList = []
      this.pcdFileList = []
      this.yamlFileList = []
      // 注意：不清空 uploadedFileNames，因为用户稍后还要保存地图时需要更新文件名
    },

    // 处理文件上传前的验证
    handleBeforeUpload(file, fileType) {
      const isValidType = this.validateFileType(file, fileType)
      if (!isValidType) {
        return false
      }

      // 创建文件对象
      const fileObj = {
        uid: file.uid,
        name: file.name,
        status: 'done',
        originFileObj: file
      }

      // 添加到对应的文件列表（maxCount="1"会自动处理文件替换）
      if (fileType === 'pgm') {
        this.pgmFileList.push(fileObj)
      } else if (fileType === 'pcd') {
        this.pcdFileList.push(fileObj)
      } else if (fileType === 'yaml') {
        this.yamlFileList.push(fileObj)
      }

      return false // 阻止自动上传
    },

    // 验证文件类型
    validateFileType(file, fileType) {
      const fileName = file.name.toLowerCase()

      if (fileType === 'pgm' && !fileName.endsWith('.pgm')) {
        message.error('只能上传PGM格式的文件')
        return false
      }

      if (fileType === 'pcd' && !fileName.endsWith('.pcd')) {
        message.error('只能上传PCD格式的文件')
        return false
      }

      if (fileType === 'yaml' && !fileName.endsWith('.yaml') && !fileName.endsWith('.yml')) {
        message.error('只能上传YAML格式的文件')
        return false
      }

      return true
    },

    // 移除文件
    handleRemoveFile(file, fileType) {
      if (fileType === 'pgm') {
        this.pgmFileList = this.pgmFileList.filter(item => item.uid !== file.uid)
      } else if (fileType === 'pcd') {
        this.pcdFileList = this.pcdFileList.filter(item => item.uid !== file.uid)
      } else if (fileType === 'yaml') {
        this.yamlFileList = this.yamlFileList.filter(item => item.uid !== file.uid)
      }
    },

    // 上传文件
    async handleUploadFiles(fileType) {
      let fileList = []

      if (fileType === 'pgm') {
        fileList = this.pgmFileList
      } else if (fileType === 'pcd') {
        fileList = this.pcdFileList
      } else if (fileType === 'yaml') {
        fileList = this.yamlFileList
      }

      if (fileList.length === 0) {
        message.warning('请先选择文件')
        return
      }

      this.uploading[fileType] = true

      try {
        const files = fileList.map(item => item.originFileObj)
        const res = await uploadMapFile(this.currentMapId, files)

        if (res.errorCode === 0) {
          message.success('文件上传成功')

          // 存储上传后的新文件名
          if (res.data) {
            this.uploadedFileNames[fileType] = res.data
            // 标记本次会话有文件上传
            this.hasFilesUploadedInCurrentSession = true

            // 立即更新当前文件列表，显示新的文件名
            this.currentFiles[fileType] = [res.data]

            console.log(`${fileType.toUpperCase()}文件上传成功，新文件名:`, res.data)
            console.log('已立即更新文件名和文件列表')
          }

          // 清空文件列表
          if (fileType === 'pgm') {
            this.pgmFileList = []
          } else if (fileType === 'pcd') {
            this.pcdFileList = []
          } else if (fileType === 'yaml') {
            this.yamlFileList = []
          }
        } else {
          requestSuccess(res)
        }
      } catch (error) {
        requestFailed(error)
      } finally {
        this.uploading[fileType] = false
      }
    },

    // 下载文件
    async handleDownloadFile(fileName) {
      try {
        showLoading('正在下载文件...')

        const res = await downloadMapFile(this.currentMapId, fileName)

        // 创建下载链接
        const blob = new Blob([res])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        hideLoading()
      } catch (error) {
        hideLoading()
        message.error('文件下载失败')
        console.error('下载文件错误:', error)
      }
    }
  }
}
</script>

<style scoped>
/* 文件管理模态框样式 */
.file-manage-container {
  padding: 16px 0;
}

.file-section {
  padding: 16px 0;
}

.file-upload-area {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.file-list {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
}

.file-list h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.file-list .ant-list-item {
  padding: 8px 0;
}

.file-list .ant-list-item-meta-title {
  font-size: 14px;
  color: #333;
}

.file-list .ant-list-item-action a {
  color: #1890ff;
  text-decoration: none;
}

.file-list .ant-list-item-action a:hover {
  color: #40a9ff;
}
</style>
