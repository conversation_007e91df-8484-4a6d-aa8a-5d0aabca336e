# my-map

## Project setup
```
yarn install
```

### Compiles and hot-reloads for development
```
yarn serve
```

### Compiles and minifies for production
```
yarn build
```

### Lints and fixes files
```
yarn lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).



1. 坐标系统修复：
PGM 纹理翻转：设置 texture.flipY = true 以正确显示地图方向
PGM 坐标调整：调整 Z 轴坐标计算以匹配翻转后的坐标系
PCD 坐标调整：调整 Z 轴坐标变换以与 PGM 坐标系保持一致
2. 位置元素大小自适应：
Person 类增强：添加  scale 参数支持动态缩放
自动缩放计算：根据地图分辨率自动计算合适的缩放比例
现有对象更新：地图加载后自动更新现有位置元素的缩放
3. 缩放逻辑：
基准分辨率: 0.05 米/像素 (缩放比例 1.0)
高分辨率地图 (0.025): 缩放比例 = 2.0 (人物变大)
低分辨率地图 (0.1): 缩放比例 = 0.5 (人物变小)
安全范围: 0.5 - 3.0 倍缩放，避免极端情况