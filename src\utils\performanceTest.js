/**
 * 性能测试工具
 * 用于测试和验证性能优化功能
 */

import { performanceMonitor } from './performanceMonitor.js'
import { renderOptimizer } from './renderOptimizer.js'

export class PerformanceTest {
  constructor() {
    this.testResults = []
    this.isRunning = false
  }

  /**
   * 运行性能测试
   */
  async runPerformanceTest(pgmRenderer, pcdRenderer, duration = 10000) {
    if (this.isRunning) {
      console.warn('性能测试已在运行中')
      return
    }

    this.isRunning = true
    this.testResults = []
    
    console.log(`开始性能测试，持续时间: ${duration}ms`)
    
    // 启动性能监控
    performanceMonitor.start()
    
    const startTime = Date.now()
    const testInterval = 1000 // 每秒记录一次数据
    
    const recordData = () => {
      const stats = performanceMonitor.getStats()
      const timestamp = Date.now() - startTime
      
      this.testResults.push({
        timestamp,
        fps: stats.fps,
        memoryMB: stats.memoryMB,
        points: stats.points,
        renderCalls: stats.renderCalls
      })
      
      console.log(`测试进度: ${Math.round(timestamp / duration * 100)}%`, stats)
    }
    
    // 定期记录数据
    const intervalId = setInterval(recordData, testInterval)
    
    // 测试结束
    setTimeout(() => {
      clearInterval(intervalId)
      this.isRunning = false
      
      const report = this.generateTestReport()
      console.log('性能测试完成:', report)
      
      return report
    }, duration)
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    if (this.testResults.length === 0) {
      return { error: '没有测试数据' }
    }

    const fps = this.testResults.map(r => r.fps)
    const memory = this.testResults.map(r => r.memoryMB)
    const points = this.testResults.map(r => r.points)

    return {
      duration: this.testResults[this.testResults.length - 1].timestamp,
      samples: this.testResults.length,
      fps: {
        avg: this.average(fps),
        min: Math.min(...fps),
        max: Math.max(...fps),
        stable: this.isStable(fps, 5) // FPS变化在5以内算稳定
      },
      memory: {
        avg: this.average(memory),
        min: Math.min(...memory),
        max: Math.max(...memory),
        peak: Math.max(...memory)
      },
      points: {
        avg: this.average(points),
        min: Math.min(...points),
        max: Math.max(...points)
      },
      performance: this.evaluatePerformance(fps, memory),
      rawData: this.testResults
    }
  }

  /**
   * 计算平均值
   */
  average(arr) {
    return Math.round(arr.reduce((a, b) => a + b, 0) / arr.length)
  }

  /**
   * 检查数据是否稳定
   */
  isStable(arr, threshold) {
    const max = Math.max(...arr)
    const min = Math.min(...arr)
    return (max - min) <= threshold
  }

  /**
   * 评估性能等级
   */
  evaluatePerformance(fps, memory) {
    const avgFPS = this.average(fps)
    const avgMemory = this.average(memory)
    
    let score = 0
    let grade = 'F'
    let issues = []

    // FPS评分
    if (avgFPS >= 60) {
      score += 40
    } else if (avgFPS >= 30) {
      score += 30
    } else if (avgFPS >= 20) {
      score += 20
      issues.push('FPS较低')
    } else {
      score += 10
      issues.push('FPS过低')
    }

    // 内存评分
    if (avgMemory <= 100) {
      score += 30
    } else if (avgMemory <= 200) {
      score += 25
    } else if (avgMemory <= 300) {
      score += 20
      issues.push('内存使用较高')
    } else {
      score += 10
      issues.push('内存使用过高')
    }

    // 稳定性评分
    if (this.isStable(fps, 5)) {
      score += 20
    } else if (this.isStable(fps, 10)) {
      score += 15
    } else {
      score += 10
      issues.push('FPS不稳定')
    }

    // 内存稳定性评分
    if (this.isStable(memory, 20)) {
      score += 10
    } else {
      score += 5
      issues.push('内存使用不稳定')
    }

    // 确定等级
    if (score >= 90) grade = 'A+'
    else if (score >= 80) grade = 'A'
    else if (score >= 70) grade = 'B'
    else if (score >= 60) grade = 'C'
    else if (score >= 50) grade = 'D'
    else grade = 'F'

    return {
      score,
      grade,
      issues,
      recommendation: this.getRecommendation(grade, issues)
    }
  }

  /**
   * 获取优化建议
   */
  getRecommendation(grade, issues) {
    const recommendations = []

    if (issues.includes('FPS过低') || issues.includes('FPS较低')) {
      recommendations.push('减少点云密度')
      recommendations.push('启用LOD优化')
      recommendations.push('降低渲染质量')
    }

    if (issues.includes('内存使用过高') || issues.includes('内存使用较高')) {
      recommendations.push('清理纹理缓存')
      recommendations.push('减少同时渲染的对象数量')
    }

    if (issues.includes('FPS不稳定')) {
      recommendations.push('启用垂直同步')
      recommendations.push('优化渲染循环')
    }

    if (issues.includes('内存使用不稳定')) {
      recommendations.push('检查内存泄漏')
      recommendations.push('优化资源管理')
    }

    if (recommendations.length === 0) {
      recommendations.push('性能表现良好，继续保持')
    }

    return recommendations
  }

  /**
   * 压力测试
   */
  async runStressTest(pcdRenderer, pointCounts = [50000, 100000, 200000, 300000]) {
    if (!pcdRenderer) {
      console.error('需要PCD渲染器进行压力测试')
      return
    }

    console.log('开始压力测试...')
    const results = []

    for (const pointCount of pointCounts) {
      console.log(`测试点数: ${pointCount}`)
      
      // 设置点数
      pcdRenderer.setMaxPoints(pointCount)
      
      // 等待渲染稳定
      await this.wait(2000)
      
      // 记录性能数据
      const stats = performanceMonitor.getStats()
      results.push({
        pointCount,
        fps: stats.fps,
        memoryMB: stats.memoryMB,
        renderCalls: stats.renderCalls
      })
      
      console.log(`点数 ${pointCount}: FPS=${stats.fps}, 内存=${stats.memoryMB}MB`)
    }

    return {
      testType: 'stress',
      results,
      recommendation: this.analyzeStressResults(results)
    }
  }

  /**
   * 分析压力测试结果
   */
  analyzeStressResults(results) {
    const recommendations = []
    
    // 找到FPS开始显著下降的点数
    for (let i = 1; i < results.length; i++) {
      const current = results[i]
      const previous = results[i - 1]
      
      if (current.fps < previous.fps * 0.7) { // FPS下降超过30%
        recommendations.push(`建议最大点数不超过 ${previous.pointCount}`)
        break
      }
    }
    
    // 检查内存使用
    const highMemoryResults = results.filter(r => r.memoryMB > 300)
    if (highMemoryResults.length > 0) {
      const firstHighMemory = highMemoryResults[0]
      recommendations.push(`点数超过 ${firstHighMemory.pointCount} 时内存使用过高`)
    }
    
    return recommendations
  }

  /**
   * 等待指定时间
   */
  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 获取测试结果
   */
  getTestResults() {
    return this.testResults
  }

  /**
   * 清除测试结果
   */
  clearResults() {
    this.testResults = []
  }
}

// 创建全局性能测试实例
export const performanceTest = new PerformanceTest()
