import * as THREE from 'three'

export class Obstacle {
  constructor(id, name, position = new THREE.Vector3(0, 0, 0), width = 1, height = 1, depth = 1) {
    this.id = id
    this.name = name
    this.type = 'obstacle'
    this.position = position.clone()
    this.width = width
    this.height = height
    this.depth = depth
    this.isSelected = false
    this.color = 0x8b4513 // 棕色
    
    this.createMesh()
  }
  
  createMesh() {
    // 创建长方体几何体
    const geometry = new THREE.BoxGeometry(this.width, this.height, this.depth)

    // 创建材质
    const material = new THREE.MeshLambertMaterial({
      color: this.color,
      transparent: true,
      opacity: 0.8
    })

    // 创建网格
    const meshObject = new THREE.Mesh(geometry, material)
    meshObject.position.y = this.height / 2 // 让底部贴地

    // 启用阴影
    meshObject.castShadow = true
    meshObject.receiveShadow = true

    // 创建边框线条（选中时显示）
    const wireframeGeometry = new THREE.EdgesGeometry(geometry)
    const wireframeMaterial = new THREE.LineBasicMaterial({
      color: 0x000000,
      transparent: true,
      opacity: 0.3
    })

    this.wireframe = new THREE.LineSegments(wireframeGeometry, wireframeMaterial)
    this.wireframe.position.y = this.height / 2
    this.wireframe.visible = false

    // 创建一个组来包含网格和线框
    this.group = new THREE.Group()
    this.group.add(meshObject)
    this.group.add(this.wireframe)

    // 设置组的位置为对象位置
    this.group.position.copy(this.position)

    // 设置用户数据在组上
    this.group.userData = {
      draggable: true,
      objectType: 'obstacle',
      objectId: this.id
    }

    // 主要引用指向组
    this.mesh = this.group
  }
  

  
  updateGeometry() {
    // 移除旧的几何体
    const oldMesh = this.group.children[0]
    const oldWireframe = this.group.children[1]

    this.group.remove(oldMesh)
    this.group.remove(oldWireframe)

    oldMesh.geometry.dispose()
    oldMesh.material.dispose()
    oldWireframe.geometry.dispose()
    oldWireframe.material.dispose()

    // 创建新的几何体
    const geometry = new THREE.BoxGeometry(this.width, this.height, this.depth)
    const material = new THREE.MeshLambertMaterial({
      color: this.color,
      transparent: true,
      opacity: 0.8
    })

    const newMesh = new THREE.Mesh(geometry, material)
    newMesh.position.y = this.height / 2 // 只设置Y偏移，让底部贴地
    newMesh.castShadow = true
    newMesh.receiveShadow = true

    // 创建新的线框
    const wireframeGeometry = new THREE.EdgesGeometry(geometry)
    const wireframeMaterial = new THREE.LineBasicMaterial({
      color: 0x000000,
      transparent: true,
      opacity: 0.3
    })

    const newWireframe = new THREE.LineSegments(wireframeGeometry, wireframeMaterial)
    newWireframe.position.y = this.height / 2
    newWireframe.visible = this.isSelected

    this.group.add(newMesh)
    this.group.add(newWireframe)

    this.wireframe = newWireframe
  }
  
  setSelected(selected) {
    this.isSelected = selected
    
    if (this.wireframe) {
      this.wireframe.visible = selected
    }
    
    // 高亮效果
    const meshObject = this.group.children[0]
    if (meshObject && meshObject.material) {
      if (selected) {
        meshObject.material.emissive.setHex(0x444444)
        meshObject.material.opacity = 1.0
      } else {
        meshObject.material.emissive.setHex(0x000000)
        meshObject.material.opacity = 0.8
      }
    }
  }
  
  updateProperty(property, value) {
    switch (property) {
      case 'name':
        this.name = value
        break
      case 'width':
        this.width = Math.max(0.1, value)
        this.updateGeometry()
        break
      case 'height':
        this.height = Math.max(0.1, value)
        this.updateGeometry()
        break
      case 'depth':
        this.depth = Math.max(0.1, value)
        this.updateGeometry()
        break
      case 'color': {
        this.color = value
        const meshObject = this.group.children[0]
        if (meshObject && meshObject.material) {
          meshObject.material.color.setHex(value)
        }
        break
      }
    }
  }
  
  // 更新位置
  updatePosition(newPosition) {
    this.position.copy(newPosition)
    
    if (this.group) {
      this.group.position.copy(newPosition)
      
      // 更新内部网格的Y位置以保持底部贴地
      const meshObject = this.group.children[0]
      const wireframe = this.group.children[1]
      
      if (meshObject) {
        meshObject.position.y = this.height / 2
      }
      if (wireframe) {
        wireframe.position.y = this.height / 2
      }
    }
  }
  
  // 获取边界框（用于碰撞检测）
  getBoundingBox() {
    const box = new THREE.Box3()
    if (this.mesh) {
      box.setFromObject(this.mesh)
    }
    return box
  }
  
  // 检查点是否在障碍物内部
  containsPoint(point) {
    const box = this.getBoundingBox()
    return box.containsPoint(point)
  }
  
  // 检查与另一个边界框是否相交
  intersectsBox(otherBox) {
    const box = this.getBoundingBox()
    return box.intersectsBox(otherBox)
  }
  
  // 检查与球体是否相交（用于人物碰撞检测）
  intersectsSphere(center, radius) {
    const box = this.getBoundingBox()
    const sphere = new THREE.Sphere(center, radius)
    return box.intersectsSphere(sphere)
  }
  
  // 获取最近的表面点（用于路径规划）
  getClosestSurfacePoint(point) {
    const box = this.getBoundingBox()
    const closestPoint = new THREE.Vector3()
    
    // 将点限制在边界框内
    closestPoint.copy(point)
    closestPoint.clamp(box.min, box.max)
    
    return closestPoint
  }
  
  // 检查射线是否与障碍物相交
  raycast(raycaster) {
    if (this.group && this.group.children[0]) {
      const intersects = raycaster.intersectObject(this.group.children[0])
      return intersects.length > 0 ? intersects[0] : null
    }
    return null
  }
  
  // 获取障碍物的角点（用于路径规划）
  getCornerPoints() {
    const box = this.getBoundingBox()
    const corners = [
      new THREE.Vector3(box.min.x, box.min.y, box.min.z),
      new THREE.Vector3(box.max.x, box.min.y, box.min.z),
      new THREE.Vector3(box.max.x, box.min.y, box.max.z),
      new THREE.Vector3(box.min.x, box.min.y, box.max.z),
      new THREE.Vector3(box.min.x, box.max.y, box.min.z),
      new THREE.Vector3(box.max.x, box.max.y, box.min.z),
      new THREE.Vector3(box.max.x, box.max.y, box.max.z),
      new THREE.Vector3(box.min.x, box.max.y, box.max.z)
    ]
    return corners
  }
  
  // 获取障碍物周围的导航点（用于路径规划）
  getNavigationPoints(margin = 0.5) {
    const box = this.getBoundingBox()
    const expandedBox = box.clone()
    expandedBox.expandByScalar(margin)
    
    // 在障碍物周围创建导航点
    const navPoints = [
      new THREE.Vector3(expandedBox.min.x, 0, expandedBox.min.z),
      new THREE.Vector3(expandedBox.max.x, 0, expandedBox.min.z),
      new THREE.Vector3(expandedBox.max.x, 0, expandedBox.max.z),
      new THREE.Vector3(expandedBox.min.x, 0, expandedBox.max.z)
    ]
    
    return navPoints
  }
  
  // 克隆障碍物
  clone() {
    return new Obstacle(
      this.id + '_copy',
      this.name + '_copy',
      this.position.clone(),
      this.width,
      this.height,
      this.depth
    )
  }
  
  // 销毁对象
  dispose() {
    if (this.group) {
      this.group.children.forEach(child => {
        if (child.geometry) child.geometry.dispose()
        if (child.material) child.material.dispose()
      })
    }
  }
}
