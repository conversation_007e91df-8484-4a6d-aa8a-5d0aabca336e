# 地图文件说明

本目录包含两套完整的地图文件，用于演示2D和3D地图渲染功能。

## 文件列表

### Screen 地图（原有文件）
- `screen.yaml` - 地图配置文件
- `screen.pgm` - 2D占用栅格地图
- `screen.pcd` - 3D点云数据

### Room 地图（新生成的房间地图）
- `room.yaml` - 房间地图配置文件
- `room.pgm` - 房间2D占用栅格地图
- `room.pcd` - 房间3D点云数据

## Room 地图详细信息

### 基本参数
- **实际尺寸**: 12m × 8m
- **像素尺寸**: 240 × 160 像素
- **分辨率**: 0.05 m/pixel (5cm/像素)
- **原点位置**: [-6.0, -4.0, 0]
- **点云数量**: 170,321 个点

### 房间布局

#### 🏠 房间结构
- **客厅** (左上区域)
  - 位置: 房间左侧
  - 面积: 约 6m × 4m
  - 特点: 开放式空间，连接入户门

- **卧室** (右上区域)  
  - 位置: 房间右上角
  - 面积: 约 6m × 4m
  - 特点: 私密空间，有独立门窗

- **厨房** (右下区域)
  - 位置: 房间右下角
  - 面积: 约 6m × 2m
  - 特点: L型布局，功能齐全

- **卫生间** (右下角)
  - 位置: 厨房下方
  - 面积: 约 6m × 2m
  - 特点: 独立卫浴空间

#### 🚪 门窗设置
- **入户门**: 左墙中央，宽1.5m
- **卧室门**: 客厅与卧室之间，宽1.5m  
- **卫生间门**: 厨房与卫生间之间，宽1.5m
- **客厅窗户**: 上墙，宽2m
- **卧室窗户**: 右墙，宽2m
- **厨房窗户**: 上墙，宽2m

#### 🪑 家具布置

**客厅家具**:
- L型沙发 (2m × 1.5m + 1m × 2m)
- 茶几 (1m × 0.75m)
- 电视柜 (2.5m × 0.5m)
- 装饰植物

**卧室家具**:
- 双人床 (2m × 2m)
- 衣柜 (0.75m × 2m)
- 床头柜 × 2 (0.75m × 0.75m)

**厨房设施**:
- L型橱柜 (上橱柜 + 侧橱柜)
- 厨房岛台 (1m × 1m)

**卫生间设施**:
- 马桶
- 洗手盆
- 淋浴房 (1.5m × 1m)

### 3D点云特征

#### 高度层次
- **地板**: 0.0m (浅灰色)
- **家具**: 0.0m - 0.8m (棕色)
- **墙壁**: 0.0m - 2.5m (深灰色)
- **天花板**: 2.5m (白色，稀疏采样)

#### 颜色编码
- 🔘 **地板**: RGB(200, 200, 200) - 浅灰色
- 🔘 **墙壁**: RGB(150, 150, 150) - 深灰色  
- 🔘 **家具**: RGB(139, 69, 19) - 棕色
- 🔘 **天花板**: RGB(255, 255, 255) - 白色
- 🔘 **门框**: RGB(101, 67, 33) - 深棕色
- 🔘 **窗框**: RGB(255, 255, 255) - 白色

#### 点云密度
- **地板/天花板**: 每像素1个点 (地板) / 每4像素1个点 (天花板)
- **墙壁**: 垂直方向每10cm一个点
- **家具**: 垂直方向每10cm一个点
- **装饰元素**: 门框、窗框等细节

## 使用方法

### 在应用中加载
```javascript
// 加载房间地图
const roomMapUrl = '/assets/room.pgm'
const roomConfigUrl = '/assets/room.yaml'  
const roomPcdUrl = '/assets/room.pcd'
```

### 坐标系说明
- **PGM坐标系**: 左上角为原点，X向右，Y向下
- **世界坐标系**: 左下角为原点，X向右，Y向上
- **PCD坐标系**: 与世界坐标系一致，Z向上

### 适用场景
- 室内导航演示
- 机器人路径规划
- 3D可视化展示
- SLAM算法测试
- 地图编辑功能验证

## 技术规格

### PGM格式
- 格式: P5 (二进制灰度图)
- 位深: 8位 (0-255)
- 占用阈值: 65% (像素值 > 166 为占用)
- 空闲阈值: 25% (像素值 < 64 为空闲)

### PCD格式  
- 版本: 0.7
- 字段: x, y, z, rgb
- 数据类型: 浮点数 + 无符号整数
- 存储格式: ASCII

### YAML配置
- 标准ROS地图配置格式
- 支持三值模式 (占用/空闲/未知)
- 包含完整的元数据信息
