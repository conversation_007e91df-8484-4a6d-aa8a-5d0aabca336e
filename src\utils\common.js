import { notification } from 'ant-design-vue';

/**
 * 请求错误
 * @param {} err 
 */
export function requestFailed(err) {
    console.error(err);
    notification['error']({
        message: '错误',
        description: ((err.response || {}).data || {}).message || '请求出现错误，请稍后再试',
        duration: 8,
    });
}

/**
 * 请求正常
 * @param {} response 
 */
export function requestSuccess(response) {
    if (response.errorCode !== 0) {
        notification['error']({
            message: '错误',
            description: response.errorMessage,
            duration: 8,
        });
    }
}

/**
 * 错误提示
 * @param {} response 
 */
export function notificationError(message, title) {
    notification['error']({
        message: title || '操作失败',
        description: message,
        duration: 8,
    });
}


/**
 * 错误提示
 * @param {} response 
 */
export function notificationSuccess(message, title) {
    notification['success']({
        message: title || '操作成功',
        description: message,
        duration: 8,
    });
}

export const showLoading = (text = '加载中...') => {
  // 先移除之前的loading（如果存在）
  hideLoading();

  // 创建全屏遮罩
  const overlay = document.createElement('div');
  overlay.style.position = 'fixed';
  overlay.style.top = '0';
  overlay.style.left = '0';
  overlay.style.width = '100vw';
  overlay.style.height = '100vh';
  overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
  overlay.style.zIndex = '9999';
  overlay.style.display = 'flex';
  overlay.style.justifyContent = 'center';
  overlay.style.alignItems = 'center';
  overlay.style.flexDirection = 'column';
  overlay.id = 'fullscreen-loading';

  // 创建loading容器
  const loadingContainer = document.createElement('div');
  loadingContainer.style.display = 'flex';
  loadingContainer.style.flexDirection = 'column';
  loadingContainer.style.alignItems = 'center';
  loadingContainer.style.justifyContent = 'center';
  loadingContainer.style.padding = '30px';
  loadingContainer.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
  loadingContainer.style.borderRadius = '12px';
  loadingContainer.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
  loadingContainer.style.backdropFilter = 'blur(10px)';

  // 创建旋转动画元素
  const spinner = document.createElement('div');
  spinner.style.width = '50px';
  spinner.style.height = '50px';
  spinner.style.border = '4px solid #e3e3e3';
  spinner.style.borderRadius = '50%';
  spinner.style.borderTop = '4px solid #1890ff';
  spinner.style.animation = 'spin 1s linear infinite';
  spinner.style.marginBottom = '20px';

  // 创建文字元素
  const textElement = document.createElement('div');
  textElement.textContent = text;
  textElement.style.fontSize = '16px';
  textElement.style.fontWeight = '500';
  textElement.style.color = '#333';
  textElement.style.textAlign = 'center';
  textElement.style.lineHeight = '1.5';
  textElement.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';

  // 添加CSS动画关键帧，给style标签添加特定ID
  const style = document.createElement('style');
  style.id = 'fullscreen-loading-style';
  style.textContent = `
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  `;

  // 组装元素
  loadingContainer.appendChild(spinner);
  loadingContainer.appendChild(textElement);
  overlay.appendChild(loadingContainer);

  document.body.appendChild(style);
  document.body.appendChild(overlay);
};

export const hideLoading = () => {
  const overlay = document.getElementById('fullscreen-loading');
  if (overlay) {
    overlay.remove();
  }

  // 只移除我们创建的特定style标签
  const loadingStyle = document.getElementById('fullscreen-loading-style');
  if (loadingStyle) {
    loadingStyle.remove();
  }
};

/**
 * 生成严谨的UUID
 * 使用crypto API或时间戳+随机数的组合方式确保唯一性
 * 即使在多人同时生成的情况下也能保证每个UUID都是唯一的
 * @returns {string} 格式为 xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx 的UUID字符串
 */
export function generateUUID() {
  // 优先使用crypto API（现代浏览器支持）
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }

  // 如果crypto.randomUUID不可用，使用crypto.getRandomValues
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);

    // 设置版本号（4）和变体位
    array[6] = (array[6] & 0x0f) | 0x40; // 版本4
    array[8] = (array[8] & 0x3f) | 0x80; // 变体位

    // 转换为UUID格式字符串
    const hex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    return [
      hex.slice(0, 8),
      hex.slice(8, 12),
      hex.slice(12, 16),
      hex.slice(16, 20),
      hex.slice(20, 32)
    ].join('-');
  }

  // 兜底方案：使用时间戳+高精度随机数+计数器的组合
  const timestamp = Date.now().toString(36); // 时间戳转36进制
  const highResTime = (performance.now() * 1000).toString(36); // 高精度时间
  const randomPart1 = Math.random().toString(36).substring(2, 15); // 随机数1
  const randomPart2 = Math.random().toString(36).substring(2, 15); // 随机数2

  // 添加一个全局计数器确保同一毫秒内的唯一性
  if (!window._uuidCounter) {
    window._uuidCounter = 0;
  }
  window._uuidCounter = (window._uuidCounter + 1) % 10000;
  const counter = window._uuidCounter.toString(36).padStart(3, '0');

  // 组合所有部分并格式化为标准UUID格式
  const combined = (timestamp + highResTime + randomPart1 + randomPart2 + counter).replace(/[^a-z0-9]/g, '');
  const padded = (combined + '0'.repeat(32)).substring(0, 32);

  // 格式化为标准UUID格式，并设置版本和变体位
  const formatted = [
    padded.slice(0, 8),
    padded.slice(8, 12),
    '4' + padded.slice(13, 16), // 版本4
    ((parseInt(padded.slice(16, 17), 16) & 0x3) | 0x8).toString(16) + padded.slice(17, 20), // 变体位
    padded.slice(20, 32)
  ].join('-');

  return formatted;
}