# 3D地图编辑器使用指南

## 快速开始

1. 启动应用：`npm run serve`
2. 在浏览器中打开：http://localhost:8080/

## 基本操作

### 添加对象
1. 从右侧面板的"元素"区域拖拽"👤 机器人"到3D场景中
2. 对象会在鼠标释放的位置创建

### 选择和编辑对象
1. 点击3D场景中的对象或右侧"场景对象"列表中的项目来选择
2. 选中的对象会高亮显示
3. 在"属性编辑"区域可以修改：
   - 名称
   - X、Y、Z坐标
   - 朝向角度（仅限机器人）

### 场景导航
- 鼠标左键拖拽：旋转场景视角
- 鼠标滚轮：缩放场景
- 鼠标右键拖拽：平移场景视角

## 导出导入功能

### 导出场景
1. 在场景中创建一些对象
2. 点击"导出场景"按钮
3. 系统会自动下载JSON文件

### 导入场景
1. 点击"导入场景"按钮
2. 选择JSON格式的场景文件
3. 确认是否替换当前场景
4. 场景会根据文件数据重建

### 测试导入功能
项目包含示例文件 `example-scene.json`，可用于测试导入功能。

## 其他功能

### 复制对象
- 选中对象后点击"复制对象"按钮
- 新对象会在原对象旁边创建

### 清空场景
- 点击"清空场景"按钮可删除所有对象
- 此操作不可撤销，请谨慎使用

### 删除单个对象
- 在"场景对象"列表中点击对象右侧的"×"按钮

## 技术特性

- 基于Three.js的3D渲染
- Vue 3响应式界面
- 实时阴影和光照
- 拖拽式对象创建
- JSON格式数据持久化

## 故障排除

如果遇到问题：
1. 检查浏览器控制台的错误信息
2. 确保使用现代浏览器（支持WebGL）
3. 重新加载页面
4. 检查网络连接

## 扩展开发

要添加新的对象类型：
1. 在 `src/classes/` 目录创建新的类文件
2. 在 `Map3D.vue` 中添加对应的创建逻辑
3. 更新导出导入功能以支持新对象类型
