# YAML配置文件集成总结

## 功能概述
集成了ROS地图配置文件（YAML）的解析和应用，使PGM渲染器能够根据真实的地图元数据进行正确的渲染和定位。

## YAML文件分析

### screen.yaml内容
```yaml
image: screen.pgm
mode: trinary
resolution: 0.025
origin: [-11.4, -8.63, 0]
negate: 0
occupied_thresh: 0.65
free_thresh: 0.25
```

### 参数含义
- **image**: 对应的PGM文件名
- **mode**: 地图模式（trinary = 三值模式：自由、占用、未知）
- **resolution**: 分辨率，每像素代表0.025米
- **origin**: 地图原点在世界坐标系中的位置 [-11.4, -8.63, 0]
- **negate**: 是否反转颜色（0 = 不反转）
- **occupied_thresh**: 占用阈值（0.65）
- **free_thresh**: 自由空间阈值（0.25）

## 新增模块：YAMLParser

### 核心功能
1. **YAML解析**: 解析简单的YAML格式文件
2. **数据类型转换**: 自动识别字符串、数字、数组、布尔值
3. **配置验证**: 验证地图配置文件的完整性
4. **错误处理**: 提供默认配置作为后备

### 主要方法
```javascript
// 解析YAML文本
YAMLParser.parseYAML(yamlText)

// 加载YAML文件
YAMLParser.loadYAML(filePath)

// 验证地图配置
YAMLParser.validateMapConfig(config)

// 获取默认配置
YAMLParser.getDefaultMapConfig()
```

## PGM渲染器增强

### 新增功能
1. **YAML配置加载**: 自动加载对应的YAML配置文件
2. **真实尺寸渲染**: 使用分辨率计算真实世界尺寸
3. **世界坐标定位**: 根据原点设置正确的世界坐标位置
4. **阈值渲染**: 使用配置中的阈值进行像素分类

### 配置驱动的渲染

#### 尺寸计算
```javascript
// 使用YAML配置中的分辨率
const realWidth = width * config.resolution  // 像素数 × 米/像素
const realHeight = height * config.resolution

// 示例：640像素 × 0.025米/像素 = 16米
```

#### 世界坐标定位
```javascript
// YAML中的origin是地图左下角的世界坐标
const originX = config.origin[0]  // -11.4米
const originY = config.origin[1]  // -8.63米

// 地图中心的世界坐标
const centerX = originX + realWidth / 2
const centerZ = originY + realHeight / 2

// 设置网格位置
mesh.position.set(centerX, 0.01, centerZ)
```

#### 像素分类渲染
```javascript
// 使用配置中的阈值
const occupiedThresh = config.occupied_thresh  // 0.65
const freeThresh = config.free_thresh         // 0.25

// 像素分类
if (value <= freeThresh) {
  // 自由空间 - 白色
} else if (value >= occupiedThresh) {
  // 占用空间 - 黑色  
} else {
  // 未知区域 - 灰色
}
```

### 边界计算增强
```javascript
// 世界坐标系中的边界
const minX = originX              // -11.4
const maxX = originX + realWidth  // -11.4 + 16 = 4.6
const minZ = originY              // -8.63
const maxZ = originY + realHeight // -8.63 + 12 = 3.37
```

## 使用效果

### 1. 真实尺寸渲染
- ✅ **精确尺寸**: 地图显示真实世界的尺寸（16m × 12m）
- ✅ **正确比例**: 1像素 = 0.025米的精确对应
- ✅ **分辨率一致**: 与原始地图数据保持一致

### 2. 世界坐标定位
- ✅ **绝对定位**: 地图在世界坐标系中的正确位置
- ✅ **原点对应**: 地图左下角对应世界坐标(-11.4, -8.63)
- ✅ **中心计算**: 地图中心自动计算为(-3.4, -2.63)

### 3. 智能像素分类
- ✅ **阈值驱动**: 使用配置中的占用/自由阈值
- ✅ **三值模式**: 自由（白）、占用（黑）、未知（灰）
- ✅ **反转支持**: 支持negate参数的颜色反转

### 4. 兼容性保持
- ✅ **向后兼容**: 保留原有的scale参数渲染方法
- ✅ **自动降级**: YAML加载失败时使用默认配置
- ✅ **错误恢复**: 配置不完整时提供合理默认值

## 调用方式

### 新的调用方式（推荐）
```javascript
// 自动加载对应的YAML配置
await this.pgmRenderer.renderPGM('/assets/screen.pgm', '/assets/screen.yaml')

// 或者让系统自动推断YAML路径
await this.pgmRenderer.renderPGM('/assets/screen.pgm')
```

### 旧的调用方式（兼容）
```javascript
// 仍然支持scale参数的方式
await this.pgmRenderer.renderPGM('/assets/screen.pgm', 0.05)
```

## 日志输出

### 配置信息
```
YAML配置加载成功: {
  image: "screen.pgm",
  resolution: 0.025,
  origin: [-11.4, -8.63, 0],
  ...
}
```

### 尺寸信息
```
PGM尺寸: 640x480 像素
实际尺寸: 16.000x12.000 米
分辨率: 0.025 米/像素
```

### 定位信息
```
地图原点: [-11.4, -8.63]
地图中心: [-3.4, -2.63]
```

### 边界信息
```
PGM边界（世界坐标）: {
  minX: -11.4, maxX: 4.6,
  minZ: -8.63, maxZ: 3.37,
  centerX: -3.4, centerZ: -2.63,
  ...
}
```

## 技术优势

### 1. 标准化支持
- 支持ROS标准的地图配置格式
- 与机器人导航系统完全兼容
- 符合SLAM建图的标准输出

### 2. 精确性提升
- 真实世界尺寸的精确渲染
- 绝对坐标系的正确定位
- 像素级别的精确对应

### 3. 灵活性增强
- 支持不同分辨率的地图
- 支持任意原点位置的地图
- 支持多种像素分类阈值

### 4. 可维护性
- 配置与代码分离
- 易于调整渲染参数
- 便于不同地图的切换

现在PGM渲染器已经完全支持YAML配置文件，能够根据真实的地图元数据进行精确的渲染和定位！
