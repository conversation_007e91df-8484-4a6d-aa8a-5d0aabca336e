import * as THREE from 'three'

export class DragManager {
  constructor(scene, camera, domElement) {
    this.scene = scene
    this.camera = camera
    this.domElement = domElement
    this.raycaster = new THREE.Raycaster()
    this.mouse = new THREE.Vector2()
    this.isDragging = false
    this.dragObject = null
    this.dragPlane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0)
    this.offset = new THREE.Vector3()

    // 拖拽边界设置
    this.bounds = {
      minX: -10,
      maxX: 10,
      minZ: -10,
      maxZ: 10,
      minY: 0
    }

    // 性能优化设置
    this.performanceMode = false // 是否启用性能模式
    this.draggableObjects = [] // 缓存可拖拽对象，避免每次遍历整个场景
    this.lastUpdateTime = 0
    this.updateThrottle = 16 // 限制更新频率到60fps

    this.setupEventListeners()
    this.setupRaycasterOptimization()
  }

  // 设置射线检测器优化参数
  setupRaycasterOptimization() {
    // 优化射线检测参数以提高性能
    this.raycaster.params.Points.threshold = 0.1
    this.raycaster.params.Line.threshold = 0.1
    this.raycaster.params.Mesh = {}

    // 设置射线的远近裁剪面，避免检测过远的对象
    this.raycaster.near = 0.1
    this.raycaster.far = 1000
  }
  
  setupEventListeners() {
    this.domElement.addEventListener('mousedown', this.onMouseDown.bind(this))
    this.domElement.addEventListener('mousemove', this.onMouseMove.bind(this))
    this.domElement.addEventListener('mouseup', this.onMouseUp.bind(this))
    this.domElement.addEventListener('contextmenu', this.onContextMenu.bind(this))
  }
  
  updateMousePosition(event) {
    const rect = this.domElement.getBoundingClientRect()
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1
  }
  
  onMouseDown(event) {
    // 只处理左键点击
    if (event.button !== 0) return

    this.updateMousePosition(event)
    this.raycaster.setFromCamera(this.mouse, this.camera)

    // 性能优化：优先检测缓存的可拖拽对象
    let intersects = []
    if (this.performanceMode && this.draggableObjects.length > 0) {
      // 性能模式：只检测已知的可拖拽对象
      intersects = this.raycaster.intersectObjects(this.draggableObjects, true)
    } else {
      // 正常模式：检测所有对象，但排除地图网格以提高性能
      const objectsToTest = this.scene.children.filter(child => {
        // 排除PGM地图网格和PCD点云，它们不需要拖拽检测
        return child.name !== 'pgm-map' &&
               child.userData?.type !== 'pcd' &&
               child.userData?.type !== 'pgm'
      })
      intersects = this.raycaster.intersectObjects(objectsToTest, true)
    }

    for (let intersect of intersects) {
      const object = intersect.object

      // 找到可拖拽的根对象（可能是父级Group）
      const draggableObject = this.findDraggableParent(object)

      if (draggableObject) {
        this.isDragging = true
        this.dragObject = draggableObject

        // 计算拖拽偏移量
        const intersectPoint = new THREE.Vector3()
        this.raycaster.ray.intersectPlane(this.dragPlane, intersectPoint)

        // 使用实际的mesh位置来计算偏移量，确保拖拽准确
        const meshPosition = new THREE.Vector3()
        draggableObject.getWorldPosition(meshPosition)
        this.offset.copy(intersectPoint).sub(meshPosition)

        // 禁用轨道控制器
        if (this.controls) {
          this.controls.enabled = false
        }

        // 触发拖拽开始事件
        this.onDragStart(draggableObject, intersect.point)
        break
      }
    }
  }
  
  onMouseMove(event) {
    if (!this.isDragging || !this.dragObject) return

    // 性能优化：限制更新频率
    const currentTime = performance.now()
    if (currentTime - this.lastUpdateTime < this.updateThrottle) {
      return
    }
    this.lastUpdateTime = currentTime

    this.updateMousePosition(event)
    this.raycaster.setFromCamera(this.mouse, this.camera)

    // 计算新位置
    const intersectPoint = new THREE.Vector3()
    this.raycaster.ray.intersectPlane(this.dragPlane, intersectPoint)

    const newPosition = intersectPoint.sub(this.offset)

    // 限制拖拽范围
    newPosition.x = Math.max(this.bounds.minX, Math.min(this.bounds.maxX, newPosition.x))
    newPosition.z = Math.max(this.bounds.minZ, Math.min(this.bounds.maxZ, newPosition.z))
    newPosition.y = Math.max(this.bounds.minY, newPosition.y)

    // 更新对象位置
    this.dragObject.position.copy(newPosition)

    // 如果对象有自定义的位置更新逻辑，调用它
    if (this.dragObject.userData && this.dragObject.userData.objectType) {
      this.updateCustomObjectPosition(this.dragObject, newPosition)
    }

    // 触发拖拽中事件
    this.onDragMove(this.dragObject, newPosition)
  }
  
  onMouseUp() {
    if (this.isDragging && this.dragObject) {
      // 触发拖拽结束事件
      this.onDragEnd(this.dragObject, this.dragObject.position)

      this.isDragging = false
      this.dragObject = null

      // 重新启用轨道控制器
      if (this.controls) {
        this.controls.enabled = true
      }
    }
  }
  
  onContextMenu(event) {
    event.preventDefault()
  }
  
  // 事件回调函数，可以被外部重写
  onDragStart(object) {
    // 可以在这里添加拖拽开始的视觉反馈
    if (object.material && object.material.emissive) {
      object.material.emissive.setHex(0x444444)
    }
  }

  onDragMove(object) {
    // 可以在这里添加拖拽过程中的逻辑
    // 例如碰撞检测、吸附等
    console.log('Dragging object:', object.userData?.objectType)
  }

  onDragEnd(object) {
    // 可以在这里添加拖拽结束的逻辑
    if (object.material && object.material.emissive) {
      object.material.emissive.setHex(0x000000)
    }
  }
  
  // 设置轨道控制器引用
  setControls(controls) {
    this.controls = controls
  }

  // 设置拖拽边界
  setBounds(bounds) {
    this.bounds = {
      minX: bounds.minX !== undefined ? bounds.minX : this.bounds.minX,
      maxX: bounds.maxX !== undefined ? bounds.maxX : this.bounds.maxX,
      minZ: bounds.minZ !== undefined ? bounds.minZ : this.bounds.minZ,
      maxZ: bounds.maxZ !== undefined ? bounds.maxZ : this.bounds.maxZ,
      minY: bounds.minY !== undefined ? bounds.minY : this.bounds.minY
    }
    console.log('DragManager bounds updated:', this.bounds)
  }

  // 重置为默认边界
  resetBounds() {
    this.bounds = {
      minX: -10,
      maxX: 10,
      minZ: -10,
      maxZ: 10,
      minY: 0
    }
    console.log('DragManager bounds reset to default')
  }
  
  // 添加可拖拽对象
  makeDraggable(object) {
    if (object.userData) {
      object.userData.draggable = true
    } else {
      object.userData = { draggable: true }
    }
  }
  
  // 移除拖拽功能
  makeNonDraggable(object) {
    if (object.userData) {
      object.userData.draggable = false
    }
  }
  
  // 检查对象是否可拖拽
  isDraggableObject(object) {
    return object.userData && object.userData.draggable === true
  }

  // 查找可拖拽的父对象
  findDraggableParent(object) {
    let current = object

    // 向上遍历对象层次结构，寻找可拖拽的对象
    while (current) {
      if (current.userData && current.userData.draggable) {
        return current
      }
      current = current.parent
    }

    return null
  }

  // 更新自定义对象的位置
  updateCustomObjectPosition(meshObject, newPosition) {
    const objectType = meshObject.userData.objectType
    const objectUuid = meshObject.userData.objectUuid

    // 通过事件通知主应用更新对象位置
    if (this.onObjectPositionUpdate) {
      this.onObjectPositionUpdate(objectType, objectUuid, newPosition)
    }
  }

  // 设置对象位置更新回调
  setObjectPositionUpdateCallback(callback) {
    this.onObjectPositionUpdate = callback
  }

  // 启用性能模式
  enablePerformanceMode() {
    this.performanceMode = true
    this.updateThrottle = 32 // 降低到30fps更新频率
    console.log('DragManager性能模式已启用')
  }

  // 禁用性能模式
  disablePerformanceMode() {
    this.performanceMode = false
    this.updateThrottle = 16 // 恢复到60fps更新频率
    console.log('DragManager性能模式已禁用')
  }

  // 更新可拖拽对象缓存
  updateDraggableObjectsCache(sceneObjects) {
    this.draggableObjects = []
    sceneObjects.forEach(obj => {
      if (obj.mesh && obj.mesh.userData && obj.mesh.userData.draggable) {
        this.draggableObjects.push(obj.mesh)
      }
    })
    console.log(`已更新可拖拽对象缓存，共${this.draggableObjects.length}个对象`)
  }

  // 根据地图复杂度自动调整性能设置
  autoAdjustPerformance(mapComplexity) {
    if (mapComplexity > 1000000) { // 超过100万像素的地图
      this.enablePerformanceMode()
    } else {
      this.disablePerformanceMode()
    }
  }

  // 销毁管理器
  dispose() {
    this.domElement.removeEventListener('mousedown', this.onMouseDown.bind(this))
    this.domElement.removeEventListener('mousemove', this.onMouseMove.bind(this))
    this.domElement.removeEventListener('mouseup', this.onMouseUp.bind(this))
    this.domElement.removeEventListener('contextmenu', this.onContextMenu.bind(this))
  }
}
